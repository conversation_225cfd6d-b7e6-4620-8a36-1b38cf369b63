fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

### beta

```sh
[bundle exec] fastlane beta
```



### playstore

```sh
[bundle exec] fastlane playstore
```



### release

```sh
[bundle exec] fastlane release
```

Deploy a new internal version to the Google Play Store

### dev

```sh
[bundle exec] fastlane dev
```

Deploy a new internal version to the Google Play Store

### distribute

```sh
[bundle exec] fastlane distribute
```

Deploy to firebase internal release

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
