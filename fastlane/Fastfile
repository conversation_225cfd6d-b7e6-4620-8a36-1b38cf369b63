# More documentation about how to customize your build
# can be found here:
# https://docs.fastlane.tools
fastlane_version "2.68.0"

# This value helps us track success metrics for Fastfiles
# we automatically generate. Feel free to remove this line
# once you get things running smoothly!
generated_fastfile_id "3e39eb86-7972-49a6-802d-c49323024847"

default_platform :android

# Fastfile actions accept additional configuration, but
# don't worry, fastlane will prompt you for required
# info which you can add here later
lane :beta do
  # build the release variant
  build_android_app(task: "assembleRelease")

  # upload to Google Play
  supply(track: "beta")

  # slack(
  #   slack_url: "https://hooks.slack.com/services/IDS"
  # )
end

lane :playstore do
  gradle(
    task: 'assemble',
    build_type: 'Release',
    properties: {
      "android.injected.signing.store.file" => "signing",
      "android.injected.signing.store.password" => "peakery",
      "android.injected.signing.key.alias" => "peakery",
      "android.injected.signing.key.password" => "peakery",
    }
  )
end

desc 'Deploy a new internal version to the Google Play Store'
lane :release do
    gradle(
      task: 'assemble',
      build_type: 'Release',
      properties: {
        "android.injected.signing.store.file" => "signing",
        "android.injected.signing.store.password" => "peakery",
        "android.injected.signing.key.alias" => "peakery",
        "android.injected.signing.key.password" => "peakery",
      }
    )
    supply(
      track: 'internal',
      package_name: 'com.peakery.android',
      apk: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH].to_s,
     
      skip_upload_images: true,
      skip_upload_changelogs: true,
      skip_upload_metadata: true,
      skip_upload_screenshots: true
    )
end

desc 'Deploy a new internal version to the Google Play Store'
lane :dev do
    gradle(
      task: 'assemble',
      build_type: 'devRelease',
      properties: {
        "android.injected.signing.store.file" => "signing",
        "android.injected.signing.store.password" => "peakery",
        "android.injected.signing.key.alias" => "peakery",
        "android.injected.signing.key.password" => "peakery",
      }
    )
    supply(
      track: 'internal',
      package_name: 'com.peakery.android.dev',
      apk: lane_context[SharedValues::GRADLE_APK_OUTPUT_PATH].to_s,
      skip_upload_images: true,
      skip_upload_changelogs: true,
      skip_upload_metadata: true,
      skip_upload_screenshots: true
    )
end

desc "Deploy to firebase internal release"
lane :distribute do
    gradle(
      task: 'assemble',
      build_type: 'Release',
      properties: {
        "android.injected.signing.store.file" => "signing",
        "android.injected.signing.store.password" => "peakery",
        "android.injected.signing.key.alias" => "peakery",
        "android.injected.signing.key.password" => "peakery",
      }
    )
    firebase_app_distribution(
        service_credentials_file: "./fastlane/firebase-service-key.json",
        firebase_cli_token: "1//04r1dhjThdu6CCgYIARAAGAQSNwF-L9IrL2AZC3vQLloLMXC7m6snsLcXY2u5OenbpLc1Hmf8TY5iTBtiolMj7xt5EQ6jBFz7R9Q",
        app: "1:778860262851:android:7fd52ce4e0102aaba5f976",
        testers: "<EMAIL>"
    )
end
