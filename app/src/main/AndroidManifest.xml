<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION"/>
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application
            android:name=".core.base.PeakeryApplication"
            android:requestLegacyExternalStorage="true"
            android:allowBackup="true"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:supportsRtl="true"
            android:theme="@style/AppTheme"
            tools:ignore="LockedOrientationActivity"
            android:usesCleartextTraffic="${usesCleartextTraffic}"
            android:largeHeap="true">
        <activity android:name=".feature.auth.AuthActivity"
                  android:theme="@style/AppTheme.NoActionBar"
                  android:windowSoftInputMode="stateHidden|adjustResize"
                  android:screenOrientation="portrait"/>
        <activity android:name=".feature.history.HistoryDetailActivity"/>
        <activity android:name=".feature.history.HistoryActivity"
                android:parentActivityName=".feature.settings.AdvancedSettingsActivity"/>
        <activity android:name=".feature.webview.WebViewActivity"
                  android:theme="@style/AppTheme.NoActionBar"
                  android:windowSoftInputMode="adjustPan"
                  android:screenOrientation="portrait" />
        <activity android:name=".feature.locationrequired.LocationRequiredActivity"
                android:theme="@style/AppTheme.NoActionBar"
                android:screenOrientation="portrait"/>
        <activity android:name=".feature.settings.AdvancedSettingsActivity"
                android:parentActivityName=".feature.settings.SettingsActivity"
                android:screenOrientation="portrait"/>
        <activity android:name=".feature.settings.SettingsActivity"
                android:parentActivityName=".feature.main.MainActivity"
                android:screenOrientation="portrait"/>
        <activity android:name=".feature.settings.DeveloperSettingsActivity"
                android:parentActivityName=".feature.settings.SettingsActivity"/>
        <activity android:name=".feature.logging.finder.FinderActivity"
                android:parentActivityName=".feature.logging.finder.FinderActivity"
                android:label="@string/pick_gpx_file"
                android:screenOrientation="portrait"/>
        <activity android:name="com.peakery.android.feature.logging.LoggingActivity"
                android:parentActivityName=".feature.main.MainActivity"
                android:theme="@style/AppTheme.LogForm"
                android:screenOrientation="portrait"
                android:exported="true"
                tools:ignore="AppLinkUrlError">
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.ALTERNATIVE"/>
                <category android:name="android.intent.category.SELECTED_ALTERNATIVE"/>
                <data android:mimeType="*/*"/>
                <data android:scheme="file"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.gpx"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="text/gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="text/xml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/gpx+xml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:host="*" android:mimeType="application/octet-stream" android:pathPattern=".*\\.gpx"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.ALTERNATIVE"/>
                <category android:name="android.intent.category.SELECTED_ALTERNATIVE"/>
                <data android:mimeType="*/*"/>
                <data android:scheme="file"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.kml"/>
                <data android:pathPattern=".*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.kml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.kml"/>
                <data android:pathPattern=".*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.kml"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.kml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/kml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/vnd.google-earth.kml+xml"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <category android:name="android.intent.category.ALTERNATIVE"/>
                <category android:name="android.intent.category.SELECTED_ALTERNATIVE"/>
                <data android:mimeType="*/*"/>
                <data android:scheme="file"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.kmz"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:scheme="http"/>
                <data android:scheme="https"/>
                <data android:host="*"/>
                <data android:pathPattern=".*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\.kmz"/>
                <data android:pathPattern=".*\\..*\\..*\\..*\\..*\\.kmz"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/vnd.google-earth.kmz"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/vnd.google-earth.kmz"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.SEND"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="*/*"/>
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW"/>
                <category android:name="android.intent.category.DEFAULT"/>
                <category android:name="android.intent.category.BROWSABLE"/>
                <data android:mimeType="application/zip"/>
            </intent-filter>
        </activity>
        <activity android:name="com.peakery.android.feature.logging.map.PeakSelectionActivity"
                android:theme="@style/AppTheme.LogForm"
                android:windowSoftInputMode="adjustPan"
                android:screenOrientation="portrait"/>
        <activity
                android:name=".feature.main.MainActivity"
                android:theme="@style/AppTheme.Translucent"
                android:screenOrientation="portrait"
                android:exported="true"
                android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <service
                android:name=".core.location.TrackerService"
                android:foregroundServiceType="location"
                android:enabled="true" />

        <meta-data
                android:name="io.fabric.ApiKey"
                android:value="5d0323e5e6131904022d10afb25b30b4b30fe71f"/>

        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.provider"
                android:exported="false"
                android:grantUriPermissions="true">
            <!-- ressource file to create -->
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/file_paths">
            </meta-data>
        </provider>
    </application>

</manifest>
