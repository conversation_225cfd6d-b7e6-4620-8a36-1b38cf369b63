package com.peakery.android.feature.tracking

import android.Manifest
import android.content.Context
import android.os.Build
import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.Analytics
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.db.LocalDao
import com.peakery.android.core.model.Point
import com.peakery.android.core.model.Track
import com.peakery.android.core.model.TrackUploadResults
import com.peakery.android.core.network.S3Uploader
import com.peakery.android.core.network.S3_AUTH_TOKEN
import com.peakery.android.core.network.S3_BUCKET_NAME
import com.peakery.android.core.network.UploadService
import com.peakery.android.core.repository.ImageSelectorRepository
import com.peakery.android.core.repository.ImageUploadStatus
import com.peakery.android.core.repository.SelectedImage
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.location.Tracker
import com.peakery.android.feature.tracking.util.GPXObservable
import com.peakery.android.getBatteryLevel
import io.jenetics.jpx.GPX
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONObject
import retrofit2.Response
import java.io.File
import java.util.concurrent.TimeUnit

class TrackingControlsViewModel(private val tracker: Tracker,
                                private val stateMachine: StateMachine,
                                private val uploadService: UploadService,
                                private val s3Uploader: S3Uploader,
                                private val dao: LocalDao,
                                private val imageSelector: ImageSelectorRepository,
                                private val userRepository: UserRepository,
                                private val analytics: Analytics): PeakeryViewModel() {

    val tracking = MutableLiveData<Tracker.TrackingState>()
    val selectedImages = MutableLiveData<List<SelectedImage>>()
    val trackSubmissionError = MutableLiveData<String>()
    val gpsState = MutableLiveData<Tracker.GpsState>()

    init {
        safeSubscribe(tracker.trackingState.subscribe { tracking.postValue(it) })
        safeSubscribe(imageSelector.selectedImagesObservable.subscribe { selectedImages.postValue(it) })
        safeSubscribe(tracker.gpsState.subscribeBy(
            onNext = {
                gpsState.postValue(it)
            }
        ))
    }

    override fun onCleared() {
        super.onCleared()
        imageSelector.endSession()
    }

    fun deleteTrack() = tracker.deleteTrack()

    fun finishTracking() = tracker.finishTracking()

    fun pauseTracking() = tracker.pauseTracking()

    fun resumeTracking() = tracker.resumeTracking()

    fun startTracking() = tracker.startTracking()

    fun recordNew() = tracker.recordNew()

    fun logPast() = tracker.logPast()

    fun saveTrack(context: Context, notes: String): Observable<Response<TrackUploadResults>> {
        val s3authToken = S3_AUTH_TOKEN
        lateinit var gpxFile: File

        val signedUrlParams = JSONObject()
        signedUrlParams.put("auth_token", s3authToken)
        signedUrlParams.put("bucket_name", S3_BUCKET_NAME)
        signedUrlParams.put("content_type", "application/xml")

        Log.e("saveTrackDebug", "#0 Starts: Get Image Upload Observable")
        return imageSelector.getUploadCompletionObservable()
            .filter { it == ImageUploadStatus.COMPLETED || it == ImageUploadStatus.FAILED }
            .doOnError {
                trackSubmissionError.postValue("We couldn't upload your images at this time. Please check your internet connection and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
            .concatMap {
                Log.e("saveTrackDebug", "#0 Ends: Get Image Upload Observable")
                Log.e("saveTrackDebug", "#1 Starts: Generate GPX File")
                if (it == ImageUploadStatus.COMPLETED) {
                    getGpx(context)
                } else {
                    Log.e("saveTrackDebug", "#0 Error: Get Image Upload Observable")
                    Log.e("saveTrackDebug", "Image Upload Status: $it")
                    throw Error("Image Upload Failed")
                }
            }
            .doOnError {
                trackSubmissionError.postValue("An error occurred generating your GPX data. Please verify your phone's storage and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
            .flatMap {
                Log.e("saveTrackDebug", "#1 Ends: Generate GPX File")
                Log.e("saveTrackDebug", "#2 Starts: Get GPX File Signed Url")

                gpxFile = it.file!!
                Log.e("Saving Track...", "GPX File Ready | File name:: ${gpxFile.name}")
                Log.e("Saving Track...", gpxFile.readText())

                signedUrlParams.put("object_name", "gpx/${gpxFile.name}")
                uploadService.getSignedUploadUrl(signedUrlParams.toString())
                    .subscribeOn(Schedulers.io())
            }
            .doOnError {
                trackSubmissionError.postValue("An error occurred while uploading your track. Please check your internet connection and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
            .flatMap {
                Log.e("saveTrackDebug", "#2 Ends: Get GPX File Signed Url")
                Log.e("saveTrackDebug", "#3 Starts: Upload GPX to signed URL")

                if (it.isSuccessful) {
                    val signedUrl = it.body()!!.data
                    val gpxBody = RequestBody.create("application/xml".toMediaTypeOrNull(), gpxFile)

                    s3Uploader.upload(signedUrl.url, gpxBody)
                        .subscribeOn(Schedulers.io())
                } else {
                    Log.e("saveTrackDebug", "#3 Error: Upload GPX to signed URL")
                    throw Error("uploadService.getSignedUploadUrl received an unexpected response")
                }
            }
            .doOnError {
                trackSubmissionError.postValue("An error occurred while uploading your track. Please check your internet connection and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
            .flatMap {
                Log.e("saveTrackDebug", "#3 Ends: Upload GPX to signed URL")
                Log.e("saveTrackDebug", "#4 Starts: Upload Track...")

                if (it.isSuccessful) {
                    val trackUploadParams = JSONObject()

                    trackUploadParams.put("auth_token", userRepository.getUserToken())
                    trackUploadParams.put("gpx_filename", gpxFile.name)
                    trackUploadParams.put("track_notes", notes)
                    trackUploadParams.put("photos", imageSelector.getUploadedUuidJsonArray())
                    trackUploadParams.put("moving_time", tracker.currentTrack.elapsedTime)

                    uploadService.uploadTrack(trackUploadParams.toString())
                } else {
                    Log.e("saveTrackDebug", "#4 Error")
                    throw Error("s3Upload.upload received an unexpected response")
                }
            }
            .doOnError {
                trackSubmissionError.postValue("An error occurred while uploading your track. Please check your internet connection and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
            .observeOn(AndroidSchedulers.mainThread())
            .doOnNext {
                Log.e("saveTrackDebug", "#5 Ends: Upload Track...")

                if (it.isSuccessful) {
                    imageSelector.endSession()
                    logBattery(tracker.currentTrack, context)
                    tracker.saveTrack()
                } else {
                    Log.e("saveTrackDebug", "#5 Error: Upload Track...")
                    Log.e("onSave error", it.errorBody().toString())
                    trackSubmissionError.postValue(it.errorBody().toString())
                }
            }
            .doOnError {
                trackSubmissionError.postValue("An error occurred while uploading your track. Please check your internet connection and try again.")
                analytics.logError(Analytics.Error.SUBMISSION, it)
            }
    }

    private fun logBattery(currentTrack: Track, context: Context) {
        val startBattery = currentTrack.startBattery?: return
        val elapsedTime = TimeUnit.SECONDS.toMinutes(currentTrack.elapsedTime)

        if (elapsedTime < 30) {
            return
        }

        val endBattery = context.getBatteryLevel()
        val totalBattery = startBattery - endBattery

        if (totalBattery <= 0) {
            return
        }

        val hourlyBattery = totalBattery / elapsedTime * 60
        analytics.logBattery(hourlyBattery)
        Log.e("battery debugging", "hourly battery use: $hourlyBattery")
    }

    fun closeTracking() {
        imageSelector.endSession()
        tracker.deleteTrack()
    }

    fun getGpx(context: Context): Observable<GPXObservable> {
        lateinit var track: Track
        return tracker.trackObservable
            .subscribeOn(Schedulers.io())
            .flatMap {
                track = it
                Observable.fromCallable { dao.getLocations(track.id) }
                    .subscribeOn(Schedulers.io())
            }
            .flatMap { points ->
                Observable.fromCallable {
                    try {
                        val gpx = createGpxFile(points)
                        val outputFile = File.createTempFile("peakery-${track.id}", ".gpx", context.cacheDir)
                        outputFile.deleteOnExit()
                        GPX.write(gpx, outputFile.path)
                        GPXObservable(
                            true,
                            outputFile
                        )
                    } catch (e: Error) {
                        e.printStackTrace()
                        GPXObservable(
                            false,
                            null
                        )
                    }
                }
            }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
    }

    fun getReadStoragePermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
    }


    private fun createGpxFile(trackPoints: List<Point>): GPX? {
        return GPX.builder().addTrack { trackBuilder ->
            trackBuilder.addSegment { segment ->
                trackPoints.forEach { point ->
                    segment.addPoint { newPoint ->
                        newPoint.lat(point.latitude)
                        newPoint.lon(point.longitude)
                        newPoint.ele(point.elevation)
                        newPoint.time(point.time)
                    }
                }
            }
        }
            .creator("peakery Android")
            .build()
    }

    fun deleteImage(selectedImage: SelectedImage) {
        imageSelector.deleteImage(selectedImage)
    }
}