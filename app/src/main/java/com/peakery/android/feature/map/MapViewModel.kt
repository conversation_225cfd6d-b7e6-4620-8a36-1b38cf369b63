package com.peakery.android.feature.map

import android.annotation.SuppressLint
import android.content.Context
import android.location.Location
import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.github.pwittchen.reactivenetwork.library.rx2.ReactiveNetwork
import com.google.android.gms.common.api.ResolvableApiException
import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.peakery.android.core.Analytics
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.model.Track
import com.peakery.android.core.model.latLng
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.StateUpdate
import com.peakery.android.feature.layers.Layer
import com.peakery.android.feature.layers.LayerRepository
import com.peakery.android.feature.webview.WebViewOverrideCoordinator
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import java.lang.Error

@SuppressLint("CheckResult", "MissingPermission")
class MapViewModel(private val tracker: Tracker,
                   private val layerRepository: LayerRepository,
                   private val peakRepository: PeakRepository,
                   private val stateMachine: StateMachine,
                   private val mapChanges: MapChanges,
                   private val webViewOverrideCoordinator: WebViewOverrideCoordinator,
                   private val userPreferences: UserPreferences,
                   private val context: Context,
                   private val analytics: Analytics): PeakeryViewModel() {

    val trackCompleted = MutableLiveData<Track>()
    val userPosition = MutableLiveData<Location>()
    val track = MutableLiveData<Track>()
    val tracking = MutableLiveData<Tracker.TrackingState>()
    val layer = MutableLiveData<Layer>()
    val search = MutableLiveData<Suggestion>()
    val state = MutableLiveData<StateUpdate>()
    val locationNotFound = MutableLiveData<ResolvableApiException>()
    val filtersChange = MutableLiveData<Boolean>()
    val webviewMapSelection = MutableLiveData<LatLng>()
    val showDebugLocations = MutableLiveData<Boolean>()

    var peaks = listOf<Peak>()
    var lastLatLngBounds: LatLngBounds? = null

    val peakSelection = MutableLiveData<Peak?>()

    val peaksDiscovery = MutableLiveData<List<Peak>>()
    val noPeaksResults = MutableLiveData<Boolean>()

    init {
        tracker.position.subscribe { userPosition.postValue(it) }
        tracker.trackObservable.subscribe { track.postValue(it) }
        tracker.trackCompletedObservable.subscribe { trackCompleted.postValue(it) }
        tracker.locationNotFoundObservable.subscribe { locationNotFound.postValue(it) }
        layerRepository.layerObservable.subscribe { layer.postValue(it) }
        peakRepository.peakResults.subscribe {
            if (it.body() == null) {
                noPeaksResults.postValue(true)
                analytics.logError(Analytics.Error.LOAD_PEAKS, Error(it.message()))
            } else {
                peaks = it.body()!!.data.peaks
                peaksDiscovery.postValue(peaks)

                val updatedPeakSelection =
                    peaks.find { it.latLng() == peakSelection.value?.latLng() }
                if (updatedPeakSelection != null && updatedPeakSelection != peakSelection.value) {
                    peakSelection.postValue(updatedPeakSelection)
                }
            }
        }
        peakRepository.filtersObservable.subscribe {
            if (!peakRepository.areFiltersConsumed) {
                filtersChange.postValue(true)
                updatePeakSearch()
            }
        }
        peakRepository.searchSelection.subscribe {
            peakSelection.postValue(Peak.fromSuggestion(it))
            search.postValue(it)
        }
        stateMachine.state.subscribe {  state.postValue(it) }
        safeSubscribe(tracker.trackingState.subscribe { tracking.postValue(it) })
        webViewOverrideCoordinator.mapSelected.subscribe { peak ->
            peakSelection.postValue(peak)
            webviewMapSelection.postValue(peak.latLng())
        }

        if (userPreferences.isOfflinePeaksEnabled()) {
            ReactiveNetwork.observeNetworkConnectivity(context)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .filter { !it.available() }
                .flatMapMaybe { peakRepository.getCachedPeaks() }
                .subscribe { peaksDiscovery.postValue(it) }
        }

        showDebugLocations.postValue(userPreferences.isDebugLocationsEnabled())
    }

    fun updateMapBounds(latLngBounds: LatLngBounds) {
        if (latLngBounds != lastLatLngBounds) {
            lastLatLngBounds = latLngBounds
            peakRepository.getPeaksForBounds(latLngBounds)
        }
    }

    fun allLayers() = layerRepository.allLayers

    fun lastKnownLocation(): Location? = tracker.lastKnownLocation()

    fun getTerrainUrl(): String {
        return if (true) {
            // Feet
            "mapbox://styles/peakery/cjjkkx6qa63mn2rthodesuu8m"
        } else {
            // Meters
            "mapbox://styles/peakery/cjwl1k3i33nzp1dqqhuhme9av"
        }
    }

    fun getSatelliteTopoUrl(): String {
        return if (true) {
            // Feet
            "mapbox://styles/peakery/cjjkme1dj1hz02rpb9ggw2zjd"
        } else {
            // Meters
            "mapbox://styles/peakery/cjwl1jkwp46hi1csdyzgmkdpy"
        }
    }

    fun getSatelliteUrl(): String = "mapbox://styles/peakery/cjjkmis2h63yj2ss1dhq1a3z6"

    private fun updatePeakSearch() {
        if (lastLatLngBounds != null) {
            peakRepository.getPeaksForBounds(lastLatLngBounds!!)
        }
    }

    fun onTapPeakOnMap(peakId: String?, latLng: LatLng) {
        if (peakId != null) {
            peakRepository.getPeak(peakId).subscribe {
                peakSelection.postValue(it)
            }
        } else {
            val peak = peaks.find { it.lat == latLng.latitude && it.lng == latLng.longitude }
            if (peak != null) {
                peakSelection.postValue(peak)
                return
            } else {
                Log.e("error", "Peak not found for ${latLng.latitude} / ${latLng.longitude}")
            }
        }
    }

    fun getElevationText(elevation: Double): String {
        return userPreferences.getSmallUnitFromFeet(elevation)
    }

    fun getLastTrackPoint() = tracker.lastTrackPoint()

    fun updateMapCenter(center: LatLng) {
        mapChanges.mapCenterObservable.onNext(center)
    }
}