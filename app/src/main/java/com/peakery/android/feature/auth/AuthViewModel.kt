package com.peakery.android.feature.auth

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.peakery.android.core.network.WEB_URL_LOGIN
import com.peakery.android.core.repository.UserRepository

class AuthViewModel(private val userRepository: UserRepository): ViewModel() {

    fun getUrl(): String = WEB_URL_LOGIN

    val loginLiveData = MutableLiveData<String>()

    fun onUserIdReceived(id: Int) {
        userRepository.setUserId(id)
        checkForAuthCompleted()
    }

    fun onUserNameReceived(name: String) {
        userRepository.setUserName(name)
        checkForAuthCompleted()
    }

    fun onUserTokenReceived(token: String) {
        userRepository.setUserToken(token)
        checkForAuthCompleted()
    }

    private fun checkForAuthCompleted() {
        if (userRepository.isAuthenticated()) loginLiveData.postValue(userRepository.getUserName())
    }
}