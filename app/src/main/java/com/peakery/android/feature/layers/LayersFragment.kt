package com.peakery.android.feature.layers

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.core.ui.ExpandedBottomSheetFragment
import org.koin.androidx.viewmodel.ext.android.viewModel

class LayersFragment: ExpandedBottomSheetFragment() {

    val model: LayersViewModel by viewModel()

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
        inflater.inflate(R.layout.fragment_layers, container, false)

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        val layers_grid = requireView().findViewById<RecyclerView>(R.id.layers_grid)
        model.allLayers.observe(viewLifecycleOwner, Observer {
            val adapter = LayerAdapter(it, model, (activity as PeakeryActivity))
            layers_grid.adapter = adapter
            adapter.layerSelected.observe(viewLifecycleOwner, Observer {
                dismiss()
            })
        })
    }
}
