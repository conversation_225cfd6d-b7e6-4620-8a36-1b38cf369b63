package com.peakery.android.feature.webview

import android.annotation.SuppressLint
import android.app.Activity
import androidx.lifecycle.MutableLiveData
import com.mapbox.mapboxsdk.geometry.LatLng
import com.peakery.android.BuildConfig
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.model.Peak
import com.peakery.android.core.network.WEB_URL_LATEST
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.StateUpdate

@SuppressLint("CheckResult")
class WebViewViewModel(private val stateMachine: StateMachine,
                       private val userRepository: UserRepository,
                       private val webViewOverrideCoordinator: WebViewOverrideCoordinator): PeakeryViewModel() {

    val url = MutableLiveData<String?>()
    val clearHistory = MutableLiveData<Boolean>()

    private var initialState: StateMachine.State? = null
    private var currentUserName: String? = null
    private var shouldReload = false

    init {
        userRepository.userNameSet
            .subscribe {
                if (it != currentUserName) {
                    shouldReload = true
                    currentUserName = it
                    loadUrl()
                }
            }
    }

    private fun loadUrl() {
        stateMachine.state
           .subscribe { if (shouldReload(it)) { postUrlUpdate() } }
    }

    private fun shouldReload(stateUpdate: StateUpdate): Boolean {
        return shouldReload
            .or(initialState == stateUpdate.state && stateUpdate.previousState == stateUpdate.state) // same state has been re-selected
    }

    fun setState(state: StateMachine.State) {
        initialState = state
        postUrlUpdate()
    }

    private fun postUrlUpdate() {
        val newUrl = when (initialState) {
            StateMachine.State.PROFILE -> {
                if (userRepository.isAuthenticated()) "${BuildConfig.web_url}/members/$currentUserName/"
                else null
            }
            StateMachine.State.LATEST -> WEB_URL_LATEST
            else -> null
        }

        if (newUrl != null) {
            shouldReload = false
            url.postValue(newUrl)
        }
    }

    fun shouldShowSettings(): Boolean {
        return initialState == StateMachine.State.PROFILE
    }

    fun savePeakCoordinates(peak: Peak) {
        stateMachine.updateState(StateMachine.State.EXPLORE_MAP)
        webViewOverrideCoordinator.mapSelected.onNext(peak)
    }

    fun setUrlFromActivityBundle(activity: Activity) {
        url.postValue(activity.intent.getStringExtra(URL))
    }
}