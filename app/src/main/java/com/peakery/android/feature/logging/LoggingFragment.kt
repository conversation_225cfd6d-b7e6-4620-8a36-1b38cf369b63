package com.peakery.android.feature.logging

import android.animation.Animator
import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewAnimationUtils
import android.view.WindowManager.LayoutParams
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.animation.doOnEnd
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.button.MaterialButton
import com.google.android.material.datepicker.CalendarConstraints
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.model.Peak
import com.peakery.android.core.repository.REQUEST_CODE_IMAGE_SELECTION
import com.peakery.android.core.ui.Glide4Engine
import com.peakery.android.feature.logging.map.PeakSelectionActivity
import com.peakery.android.feature.tracking.SelectedImagesAdapter
import com.peakery.android.feature.tracking.util.CalendarRangeValidator
import com.peakery.android.feature.webview.WebViewActivity
import com.peakery.android.okSnack
import com.peakery.android.snack
import com.peakery.android.toast
import com.tbruyelle.rxpermissions2.RxPermissions
import com.zhihu.matisse.Matisse
import com.zhihu.matisse.MimeType
import io.reactivex.android.schedulers.AndroidSchedulers
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.File
import java.lang.Math.max
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar
import java.util.TimeZone
import java.util.concurrent.TimeUnit

class LoggingFragment: PeakeryFragment(R.layout.fragment_manual_logging) {

    private val model: LoggingViewModel by viewModel()

    private var popup: PopupWindow? = null
    private lateinit var pulseAnimator: ObjectAnimator

    private var hasGpx = false
    private var isRevealAnimationComplete = false

    private lateinit var manual_logging_button_add_peak: TextInputEditText
    private lateinit var manual_logging_gpx_file: TextView
    private lateinit var manual_logging_date: View
    private lateinit var manual_logging_title: TextView
    private lateinit var manual_logging_button_add_more_peaks: TextView
    private lateinit var manual_logging_date_input: TextInputEditText
    private lateinit var manual_logging_button_save: Button
    private lateinit var manual_logging_image_list: RecyclerView
    private lateinit var manual_logging_peak_list: RecyclerView
    private lateinit var manual_logging_button_add_peak_layout: TextInputLayout
    private lateinit var manual_logging_button_add_photos: MaterialButton
    private lateinit var manual_logging_peaks_you_climbed: TextView
    private lateinit var manual_logging_content: View
    private lateinit var manual_logging_loading: View
    private lateinit var manual_logging_button_cancel: Button
    private lateinit var review_submit_input_body: TextInputEditText

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initView()
        initPeakSearch()
        initPeakSelectionList()
        initImageSelectionGrid()
        initObservers()
    }

    private fun initView() {
        manual_logging_button_add_peak = requireView().findViewById(R.id.manual_logging_button_add_peak)
        manual_logging_gpx_file = requireView().findViewById(R.id.manual_logging_gpx_file)
        manual_logging_date = requireView().findViewById(R.id.manual_logging_date)
        manual_logging_title = requireView().findViewById(R.id.manual_logging_title)
        manual_logging_button_add_more_peaks = requireView().findViewById(R.id.manual_logging_button_add_more_peaks)
        manual_logging_date_input = requireView().findViewById(R.id.manual_logging_date_input)
        manual_logging_button_save = requireView().findViewById(R.id.manual_logging_button_save)
        manual_logging_image_list = requireView().findViewById(R.id.manual_logging_image_list)
        manual_logging_peak_list = requireView().findViewById(R.id.manual_logging_peak_list)
        manual_logging_button_add_peak_layout = requireView().findViewById(R.id.manual_logging_button_add_peak_layout)
        manual_logging_button_add_photos = requireView().findViewById(R.id.manual_logging_button_add_photos)
        manual_logging_peaks_you_climbed = requireView().findViewById(R.id.manual_logging_peaks_you_climbed)
        manual_logging_content = requireView().findViewById(R.id.manual_logging_content)
        manual_logging_loading = requireView().findViewById(R.id.manual_logging_loading)
        manual_logging_button_cancel = requireView().findViewById(R.id.manual_logging_button_cancel)
        review_submit_input_body = requireView().findViewById(R.id.review_submit_input_body)
    }

    private fun initPeakSearch() {
        model.suggestionsResults.observe(viewLifecycleOwner, Observer {
            popup?.dismiss()

            val popupView = LayoutInflater.from(context).inflate(R.layout.popup_peak_search, null)
            popup = PopupWindow(
                popupView,
                manual_logging_button_add_peak.width,
                LayoutParams.WRAP_CONTENT
            ).apply {
                inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
                isOutsideTouchable = true
                isFocusable = false
            }
            val adapter = LoggingPeakSearchAdapter(it, requireContext(), model)
            popupView.findViewById<RecyclerView>(R.id.popup_list).adapter = adapter
            safeSubscribe(adapter.observer.subscribe { suggestion ->
                model.selectPeak(Peak.fromSuggestion(suggestion))
                popup?.dismiss()
                hideKeyboard()
            })
            popup?.showAsDropDown(manual_logging_button_add_peak)
        })
    }

    fun setGpxFile(filePath: String, fromTracking: Boolean) {
        hasGpx = true
        model.setGpxFile(filePath)

        if (!::manual_logging_gpx_file.isInitialized) return

        manual_logging_gpx_file.visibility = if (fromTracking) View.GONE else View.VISIBLE
        manual_logging_gpx_file.text = File(filePath).name

        manual_logging_date.visibility = View.GONE
        updateSaveButtonState()
    }

    fun setManualLoggingMode(peak: Peak?) {
        if (!::manual_logging_title.isInitialized) return

        manual_logging_title.visibility = View.GONE
        updateSaveButtonState()
        peak?.let { model.selectPeak(it) }
    }

    private fun updateSaveButtonState() {
        if (hasGpx) return

        val enable = manual_logging_date_input.text.isNullOrEmpty().not() && model.selectedPeaks.value?.data?.isNotEmpty()?: false
        manual_logging_button_save.isEnabled = enable
    }

    private fun initImageSelectionGrid() {
        model.selectedImages.observe(viewLifecycleOwner, Observer { selectedImages ->
            val adapter = SelectedImagesAdapter(selectedImages.reversed(), requireContext())
            manual_logging_image_list.adapter = adapter
            adapter.deleteObserver.observe(viewLifecycleOwner, Observer { uri ->
                model.deleteImage(uri)
            })
        })
    }

    private fun initPeakSelectionList() {
        val adapter = LoggingPeakSelectionAdapter(model)
        manual_logging_peak_list.adapter = adapter

        model.selectedPeaks.observe(
            viewLifecycleOwner,
            androidx.lifecycle.Observer { selectedPeaks ->
                val peaks = selectedPeaks.data

                when {
                    selectedPeaks.isLoading -> {
                        showFullscreenLoader()
                        manual_logging_button_add_more_peaks.visibility = View.GONE
                    }
                    peaks.isNotEmpty() -> {
                        dismissFullscreenLoader()
                        manual_logging_button_add_peak_layout.visibility = View.GONE
                        manual_logging_button_add_more_peaks.visibility = View.VISIBLE
                        manual_logging_peak_list.visibility = View.VISIBLE
                    }
                    else -> {
                        dismissFullscreenLoader()
                        manual_logging_button_add_peak_layout.visibility = View.VISIBLE
                        manual_logging_button_add_more_peaks.visibility = View.GONE
                        manual_logging_peak_list.visibility = View.GONE
                    }
                }
                manual_logging_peaks_you_climbed.text = if (peaks.size > 1) {
                    getString(R.string.peaks_you_climbed)
                } else {
                    getString(R.string.peak_you_climbed)
                }
                adapter.update(peaks)
                updateSaveButtonState()
            })
    }

    @SuppressLint("CheckResult")
    private fun initObservers() {
        manual_logging_button_cancel.setOnClickListener {
            MaterialAlertDialogBuilder(requireActivity(), R.style.AlertDialog)
                .setTitle(R.string.discard_confirmation)
                .setNegativeButton(getString(R.string.cancel), null)
                .setPositiveButton(R.string.confirm_delete) { _, _ ->
                    snack(R.string.track_deleted)
                    model.deleteTrack()
                    activity?.finish()
                }
                .create()
                .show()
        }
        manual_logging_button_save.setOnClickListener { model.submit(
            review_submit_input_body.text.toString(),
            manual_logging_date_input.text.toString()
        ) }
        manual_logging_button_add_peak_layout.setEndIconOnClickListener { PeakSelectionActivity.start(activity) }
        manual_logging_button_add_peak.setOnClickListener { updatePeakSearch() }
        manual_logging_button_add_peak.setOnFocusChangeListener { _, hasFocus ->  if (hasFocus) updatePeakSearch() }
        manual_logging_button_add_more_peaks.setOnClickListener { PeakSelectionActivity.start(activity) }

        manual_logging_button_add_photos.setOnClickListener {
            val permissions = RxPermissions(this)
            safeSubscribe(permissions.request(model.getReadStoragePermission()).subscribe(
                {
                    Matisse.from(activity)
                        .choose(MimeType.ofImage())
                        .maxSelectable(30)
                        .thumbnailScale(0.85f)
                        .imageEngine(Glide4Engine())
                        .forResult(REQUEST_CODE_IMAGE_SELECTION)
                },
                { okSnack("Permission required to upload photos") } // Error
            ))
        }

        RxView.clicks(manual_logging_date_input)
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe { displayDatePicker() }

        RxTextView.textChanges(manual_logging_button_add_peak)
            .debounce(500, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { updatePeakSearch() }

        manual_logging_button_add_peak.setOnKeyListener { v, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_ENTER && event.action == KeyEvent.ACTION_DOWN) {
                hideKeyboard()
            }
            false
        }

        model.submission.observe(viewLifecycleOwner, Observer { submission ->
            dismissProgressDialog()
            when (submission) {
                is Submission.Loading -> {
                    showProgressDialog(R.string.saving_log)
                }
                is Submission.Success -> {
                    startActivity(WebViewActivity.newIntent(requireContext(), submission.url))
                    activity?.finish()
                }
                is Submission.Error -> {
                    toast(requireContext(), getString(R.string.error_occurred))
                }
            }

        })

        model.error.observe(viewLifecycleOwner, Observer { error ->
            when (error) {
                is LoggingError.ParseGpxError -> {
                    Snackbar.make(requireView(), R.string.error_parsing_gpx, Snackbar.LENGTH_INDEFINITE).apply {
                        setAction(R.string.ok) {this.dismiss() }
                        view.findViewById<TextView>(com.google.android.material.R.id.snackbar_text).maxLines = 5
                        show()
                    }
//                    toast(context, getString(R.string.error_parsing_gpx))
                }
                is LoggingError.NoPeaksFound -> {
                    Snackbar.make(requireView(), R.string.gpx_no_peaks_found, Snackbar.LENGTH_INDEFINITE).apply {
                        setAction(R.string.ok) {this.dismiss() }
                        view.findViewById<TextView>(com.google.android.material.R.id.snackbar_text).maxLines = 5
                        show()
                    }
//                    toast(context, getString(R.string.gpx_no_peaks_found))
                }
                is LoggingError.GpxUploadError,
                is LoggingError.SignedUrlError,
                is LoggingError.Unknown -> {
                    MaterialAlertDialogBuilder(requireContext())
                        .setTitle(R.string.error_occurred)
                        .setMessage(R.string.gpx_error)
                        .setPositiveButton(R.string.ok, null)
                        .setOnDismissListener { activity?.finish() }
                        .show()
                }
            }
        })
    }

    private fun updatePeakSearch() {
        val text = manual_logging_button_add_peak.text!!
        if (text.length > 2 || text.isEmpty()) {
            model.searchPeaks(text.toString())
        }
    }

    private fun displayDatePicker() {
        val picker = MaterialDatePicker.Builder
            .datePicker()
            .setTitleText(R.string.date_of_climb)
            .setCalendarConstraints(getCalendarConstraints())
            .setTheme(R.style.DatePickerTheme)
            .build()

        picker.show(parentFragmentManager, picker.toString())
        picker.addOnPositiveButtonClickListener {
            val timezone = TimeZone.getDefault()
            val offset = timezone.getOffset(Date().time) * -1
            manual_logging_date_input.setText(model.epochToDisplayDate(it + offset))
            updateSaveButtonState()
        }
    }

    private fun getCalendarConstraints(): CalendarConstraints {
        val calendarStart = GregorianCalendar.getInstance()
        val calendarEnd = GregorianCalendar.getInstance()

        val currentYear = calendarStart.get(Calendar.YEAR)
        val calendarStartMinimumYear = currentYear - 50

        calendarStart.set(
            calendarStartMinimumYear,
            calendarStart.get(Calendar.MONTH),
            calendarStart.get(Calendar.DATE)
        )
        calendarEnd.set(
            currentYear, calendarStart.get(Calendar.MONTH), calendarStart.get(Calendar.DATE)
        )

        val minDate = calendarStart.timeInMillis
        val maxDate = calendarEnd.timeInMillis

        return CalendarConstraints.Builder()
            .setStart(minDate)
            .setEnd(maxDate)
            .setOpenAt(maxDate)
            .setValidator(
                CalendarRangeValidator(
                    minDate,
                    maxDate
                )
            )
            .build()
    }

    private fun hideKeyboard() {
        val view = requireActivity().findViewById<View>(android.R.id.content)
        if (view != null) {
            val imm = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }


    private fun showFullscreenLoader() {
        manual_logging_content.visibility = View.GONE
        manual_logging_loading.visibility = View.VISIBLE
        startPulseAnimation(manual_logging_loading.findViewById(R.id.loading_text_shadow))
    }

    private fun dismissFullscreenLoader() {
        if (hasGpx && !isRevealAnimationComplete) {
            isRevealAnimationComplete = true
            // delay animation to ensure UI below is properly drawn (increases smoothness)
            Handler().postDelayed({ circularReveal() }, 300)
        }
    }

    private fun startPulseAnimation(v: View) {
        pulseAnimator = ObjectAnimator.ofPropertyValuesHolder(
            v,
            PropertyValuesHolder.ofFloat("alpha", 0f, 1f, 0f )
        ).apply {
            duration = 2000
            startDelay = 1000
            doOnEnd {
                Handler().postDelayed({
                    pulseAnimator.start()
                }, 1000)
            }
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    private fun circularReveal() {
        val cx: Int = manual_logging_content.width / 2
        val cy: Int = manual_logging_content.height / 2
        val finalRadius = max(manual_logging_content.width, manual_logging_content.height)
        val circularReveal: Animator = ViewAnimationUtils.createCircularReveal(manual_logging_content, cx, cy, 0f, finalRadius.toFloat())
        circularReveal.duration = 700
        manual_logging_content.visibility = View.VISIBLE
        circularReveal.start()
    }
}
