package com.peakery.android.feature.main

import android.Manifest
import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.StateUpdate

class MainViewModel(stateMachine: StateMachine): PeakeryViewModel() {

    val data = MutableLiveData<StateUpdate>()

    init {
        safeSubscribe(stateMachine.state.subscribe { data.value = it })
    }

    fun getRequestedPermissions(): String {
        return Manifest.permission.ACCESS_FINE_LOCATION
    }
}