package com.peakery.android.feature.logging.finder

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.peakery.android.R
import com.peakery.android.core.FILE_PICKER_REQUEST_GPX
import com.peakery.android.core.base.PeakeryActivity

class FinderActivity: PeakeryActivity() {

    companion object { fun start(activity: Activity?) = activity?.startActivityForResult(Intent(activity, FinderActivity::class.java), FILE_PICKER_REQUEST_GPX) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_finder)

        val closeDrawable = getDrawable(R.drawable.ic_clear)
        closeDrawable?.setTint(getColor(R.color.white))
        supportActionBar?.setHomeAsUpIndicator(closeDrawable)
    }
}