package com.peakery.android.feature.tracking

import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Vibrator
import android.text.InputType
import android.view.View
import android.view.ViewGroup
import android.view.animation.DecelerateInterpolator
import android.view.inputmethod.EditorInfo
import android.widget.Button
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.Recycler
import androidx.transition.*
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.textfield.TextInputEditText
import com.google.firebase.analytics.FirebaseAnalytics
import com.jakewharton.rxbinding2.view.RxView
import com.nbsp.materialfilepicker.MaterialFilePicker
import com.nbsp.materialfilepicker.ui.FilePickerActivity
import com.peakery.android.*
import com.peakery.android.R
import com.peakery.android.core.FILE_PICKER_REQUEST_GPX
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.repository.REQUEST_CODE_IMAGE_SELECTION
import com.peakery.android.core.ui.Glide4Engine
import com.peakery.android.core.ui.LongPressButton
import com.peakery.android.feature.logging.LoggingActivity
import com.peakery.android.feature.logging.finder.FinderActivity
import com.peakery.android.feature.webview.WebViewActivity
import com.tbruyelle.rxpermissions2.RxPermissions
import com.zhihu.matisse.Matisse
import com.zhihu.matisse.MimeType
import io.reactivex.rxkotlin.subscribeBy
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.concurrent.TimeUnit
import java.util.regex.Pattern


class TrackingControlsFragment: PeakeryFragment(R.layout.fragment_tracking_controls) {

    private val model: TrackingControlsViewModel by viewModel()
    private val analytics: FirebaseAnalytics by inject()

    private lateinit var selectionScene: Scene
    private lateinit var manualSelectionScene: Scene
    private lateinit var onScene: Scene
    private lateinit var pauseScene: Scene
    private lateinit var preTrackingScene: Scene
    private lateinit var postTrackingScene: Scene

    lateinit var sceneTransition: Transition

    companion object { fun new() : TrackingControlsFragment = TrackingControlsFragment() }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        val tracking_controls_root = requireView().findViewById<ViewGroup>(R.id.tracking_controls_root)

        selectionScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_selection, requireContext())
        manualSelectionScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_manual_selection, requireContext())
        onScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_on, requireContext())
        pauseScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_paused, requireContext())
        preTrackingScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_not_started, requireContext())
        postTrackingScene = Scene.getSceneForLayout(tracking_controls_root, R.layout.tracking_controls_post_tracking, requireContext())

        sceneTransition = ChangeBounds()
        sceneTransition.duration = 250
        sceneTransition.interpolator = DecelerateInterpolator()

        model.tracking.observe(viewLifecycleOwner, Observer {
            when (it) {
                Tracker.TrackingState.SELECTION -> inflateSelection()
                Tracker.TrackingState.MANUAL_SELECTION -> inflateManualSelection()
                Tracker.TrackingState.TRACKING_NOT_STARTED -> inflateTrackingNotStarted()
                Tracker.TrackingState.TRACKING_ON -> inflateTrackingOn()
                Tracker.TrackingState.TRACKING_PAUSED -> inflateTrackingPaused()
                Tracker.TrackingState.POST_TRACKING-> inflateTrackingPaused() // inflatePostTracking()
            }
        })
    }

    private fun openLogging() {
        safeSubscribe(
            model.getGpx(requireActivity()).take(1).subscribeBy(
                onNext = { LoggingActivity.start(activity, it.file?.path, true) },
                onError = {
                    snack(R.string.error_occurred)
                    it.log()
                }
            )
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == FILE_PICKER_REQUEST_GPX && resultCode == RESULT_OK) {
            val file = data?.getStringExtra(FilePickerActivity.RESULT_FILE_PATH)
            if (file != null) {
                LoggingActivity.start(activity, file)
            } else {
                snack(R.string.gpx_error_try_again)
            }
        }
    }

    private fun inflateSelection() {
        analytics.setCurrentScreen(requireActivity(), "logging_selection", null)
        TransitionManager.go(selectionScene, null)
        selectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_record_new).setOnClickListener {
            model.recordNew()
        }
        selectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_log_past).setOnClickListener {
            model.logPast()
        }
        selectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_log_cancel).setOnClickListener {
            model.closeTracking()
        }
    }

    private fun inflateManualSelection() {
        analytics.setCurrentScreen(requireActivity(), "logging_manual", null)
        TransitionManager.go(manualSelectionScene, null)
        manualSelectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_gpx_file).setOnClickListener {
            val permissions = RxPermissions(this)
            safeSubscribe(permissions.request(model.getReadStoragePermission()).subscribe(
                {
                    FinderActivity.start(activity)
//                    MaterialFilePicker()
//                        .withSupportFragment(this)
//                        .withCloseMenu(true)
//                        .withFilter(Pattern.compile(".*\\.(gpx)$"))
//                        .withRequestCode(FILE_PICKER_REQUEST_GPX)
//                        .start()
                },
                {
                    it.printStackTrace()
                    snack("Permission required to select GPX files")
                } // Error
            ))
        }
        manualSelectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_log_manually).setOnClickListener {
            LoggingActivity.start(activity)
        }
        manualSelectionScene.sceneRoot.findViewById<View>(R.id.tracking_controls_log_cancel).setOnClickListener {
            model.closeTracking()
        }
    }


    @SuppressLint("CheckResult")
    private fun inflatePostTracking() {
        analytics.setCurrentScreen(requireActivity(), "tracking_finished", null)
        TransitionManager.go(postTrackingScene, Fade())

        postTrackingScene.sceneRoot.findViewById<TextInputEditText>(R.id.review_submit_input_body).imeOptions = EditorInfo.IME_ACTION_DONE
        postTrackingScene.sceneRoot.findViewById<TextInputEditText>(R.id.review_submit_input_body).setRawInputType(InputType.TYPE_TEXT_FLAG_CAP_SENTENCES)
        postTrackingScene.sceneRoot.findViewById<Button>(R.id.tracking_controls_post_tracking_delete).setOnClickListener {
            MaterialAlertDialogBuilder(requireActivity(), R.style.AlertDialog)
                .setTitle(R.string.discard_confirmation)
                .setNegativeButton(getString(R.string.cancel)) { dialog, _ ->
                    dialog.dismiss()
                }
                .setPositiveButton(R.string.confirm_delete) { dialog, _ ->
                    snack(R.string.track_deleted)
                    dialog.dismiss()
                    model.deleteTrack()
                }
                .create()
                .show()
        }

        RxView.clicks(postTrackingScene.sceneRoot.findViewById(R.id.tracking_controls_pause_stop))
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe {
                showProgressDialog(R.string.saving_log)
                safeSubscribe(model.saveTrack(requireContext(), requireView().findViewById<TextInputEditText>(R.id.review_submit_input_body).text.toString())
                    .subscribe(
                        {
                            dismissProgressDialog()
                            if (it.isSuccessful) {
                                startActivity(
                                    WebViewActivity.newIntent(
                                        requireContext(),
                                        "${BuildConfig.web_url}${it.body()!!.data.summit_url}"
                                    )
                                )
                            }
                        }, { })
                )
            }

        postTrackingScene.sceneRoot.findViewById<Button>(R.id.tracking_controls_button_add_photos).setOnClickListener {
            val permissions = RxPermissions(this)
            safeSubscribe(permissions.request(model.getReadStoragePermission()).subscribe(
                {
                    Matisse.from(activity)
                        .choose(MimeType.ofImage())
                        .maxSelectable(30)
                        .thumbnailScale(0.85f)
                        .imageEngine(Glide4Engine())
                        .forResult(REQUEST_CODE_IMAGE_SELECTION)
                },
                { okSnack("Permission required to upload photos") } // Error
            ))
        }

        model.selectedImages.observe(viewLifecycleOwner, Observer { selectedImages ->
            val adapter = SelectedImagesAdapter(selectedImages.reversed(), requireContext())
            requireView().findViewById<RecyclerView>(R.id.tracking_controls_image_list).adapter = adapter
            adapter.deleteObserver.observe(viewLifecycleOwner, Observer { uri ->
                model.deleteImage(uri)
            })
        })

        model.trackSubmissionError.observe(viewLifecycleOwner, Observer { errorMessage ->
            dismissProgressDialog()
            snack(errorMessage)
        })
    }

    private fun inflateTrackingNotStarted() {
        analytics.setCurrentScreen(requireActivity(), "tracking_not_started", null)
        TransitionManager.go(preTrackingScene, null)
        preTrackingScene.sceneRoot.findViewById<View>(R.id.tracking_controls_pause_stop).setOnClickListener {
            model.closeTracking()
        }
        preTrackingScene.sceneRoot.findViewById<View>(R.id.tracking_controls_resume).setOnClickListener {
            if (model.gpsState.value == Tracker.GpsState.ACQUIRED) {
                model.startTracking()
                vibratePlay()
            } else {
                snack(R.string.gps_not_found_start_message)
            }
        }
    }

    private fun inflateTrackingOn() {
        analytics.setCurrentScreen(requireActivity(), "tracking_on", null)
        TransitionManager.go(onScene, sceneTransition)
        onScene.sceneRoot.findViewById<LongPressButton>(R.id.tracking_controls_pause_stop).setOnLongPressListener {
            model.pauseTracking()
            vibratePlay()
        }
    }

    private fun inflateTrackingPaused() {
        analytics.setCurrentScreen(requireActivity(), "tracking_paused", null)

        TransitionManager.go(pauseScene, sceneTransition)

        pauseScene.sceneRoot.findViewById<View>(R.id.tracking_controls_resume).setOnClickListener {
            model.resumeTracking()
            vibratePlay()
        }
        pauseScene.sceneRoot.findViewById<LongPressButton>(R.id.tracking_controls_pause_stop).setOnLongPressListener {
            model.finishTracking()
            vibratePlay()
            openLogging()
        }
    }

    private fun vibratePlay() {
        val vibrator = requireContext().getSystemService(Context.VIBRATOR_SERVICE) as Vibrator
        if (vibrator.hasVibrator()) {
            vibrator.vibrate(10)
        }
    }
}
