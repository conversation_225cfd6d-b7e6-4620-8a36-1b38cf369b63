package com.peakery.android.feature.settings

import android.os.Bundle
import android.os.Handler
import android.text.InputType
import android.util.Log
import androidx.core.widget.addTextChangedListener
import androidx.preference.CheckBoxPreference
import androidx.preference.EditTextPreference
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import com.peakery.android.R
import org.koin.androidx.viewmodel.ext.android.viewModel

class DeveloperSettingsFragment: PreferenceFragmentCompat() {

    private val model: DeveloperSettingsViewModel by viewModel()

    private lateinit var gpsIntervalPreference: EditTextPreference
    private lateinit var gpsFastIntervalPreference: EditTextPreference
    private lateinit var gpsTrackingOffIntervalPreference: EditTextPreference

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        setPreferencesFromResource(R.xml.developer_settings, rootKey)

        val offlinePeaksPreference = preferenceScreen.findPreference<CheckBoxPreference>("offline_peaks")
        offlinePeaksPreference?.isChecked = model.isOfflinePeaksEnabled()

        val debugLocationPreference = preferenceScreen.findPreference<CheckBoxPreference>("debug_locations")
        debugLocationPreference?.isChecked = model.isDebugLocationsEnabled()

        gpsIntervalPreference = preferenceScreen.findPreference("gps_interval")!!
        gpsIntervalPreference.text = model.getGpsInterval().toString()
        gpsIntervalPreference.summary = model.getGpsInterval().toString() + " milliseconds"
        gpsIntervalPreference.setOnBindEditTextListener { it.inputType = InputType.TYPE_CLASS_NUMBER }
        gpsIntervalPreference.setOnPreferenceChangeListener { _, _ ->
            updatedPreference(gpsIntervalPreference)
            true
        }

        gpsFastIntervalPreference = preferenceScreen.findPreference("gps_fast_interval")!!
        gpsFastIntervalPreference.text = model.getFastGpsInterval().toString()
        gpsFastIntervalPreference.summary = model.getFastGpsInterval().toString() + " milliseconds"
        gpsFastIntervalPreference.setOnBindEditTextListener { it.inputType = InputType.TYPE_CLASS_NUMBER }
        gpsFastIntervalPreference.setOnPreferenceChangeListener { _, _ ->
            updatedPreference(gpsFastIntervalPreference)
            true
        }

        gpsTrackingOffIntervalPreference = preferenceScreen.findPreference("gps_tracking_off_interval")!!
        gpsTrackingOffIntervalPreference.text = model.getTrackingOffGpsInterval().toString()
        gpsTrackingOffIntervalPreference.summary = model.getTrackingOffGpsInterval().toString() + " milliseconds"
        gpsTrackingOffIntervalPreference.setOnBindEditTextListener { it.inputType = InputType.TYPE_CLASS_NUMBER }
        gpsTrackingOffIntervalPreference.setOnPreferenceChangeListener { _, _ ->
            updatedPreference(gpsTrackingOffIntervalPreference)
            true
        }
    }

    private fun updatedPreference(preference: EditTextPreference) {
        Handler().postDelayed({
            val interval = preference.text!!.toLong()
            when (preference) {
                gpsIntervalPreference -> model.updateGpsInterval(interval)
                gpsFastIntervalPreference -> model.updateFastGpsInterval(interval)
                gpsTrackingOffIntervalPreference -> model.updateTrackingOffInterval(interval)
            }
            preference.summary = "$interval milliseconds"
        }, 300)
    }

    override fun onPreferenceTreeClick(preference: Preference): Boolean {
        return when (preference.key) {
            "offline_peaks" -> {
                if (preference is CheckBoxPreference) {
                    model.toggleOfflinePeaks(preference.isChecked)
                    true
                }
                false
            }
            "debug_locations" -> {
                if (preference is CheckBoxPreference) {
                    model.toggleDebugLocation(preference.isChecked)
                    true
                }
                false
            }
            else -> super.onPreferenceTreeClick(preference)
        }
    }
}