package com.peakery.android.feature.history

import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.db.LocalDao
import com.peakery.android.core.model.Point
import io.reactivex.Single
import io.reactivex.schedulers.Schedulers

class HistoryDetailViewModel(private val dao: LocalDao): PeakeryViewModel() {

    val data = MutableLiveData<List<Point>>()

    fun loadPoints(id: Long) {
        safeSubscribe(
            Single.fromCallable { dao.getLocations(id) }
                .subscribeOn(Schedulers.io())
                .subscribe(
                    { data.postValue(it) },
                    { }
                )
        )
    }
}