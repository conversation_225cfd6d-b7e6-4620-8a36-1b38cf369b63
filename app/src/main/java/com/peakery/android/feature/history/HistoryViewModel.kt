package com.peakery.android.feature.history

import android.Manifest
import android.content.Context
import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.db.LocalDao
import com.peakery.android.core.model.Point
import com.peakery.android.core.model.Track
import com.peakery.android.feature.tracking.util.GPXObservable
import io.jenetics.jpx.GPX
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.flatMapIterable
import io.reactivex.schedulers.Schedulers
import java.io.File

class HistoryViewModel(private val dao: LocalDao): PeakeryViewModel() {

    var data = MutableLiveData<List<HistoryData>>()

    fun loadHistory() {
        safeSubscribe(
            Observable.fromCallable { dao.getTracks() }
                .flatMapIterable { it }
                .map { track ->
                    HistoryData(
                        track,
                        dao.getLocations(track.id)
                    )
                }
                .toList()
                .subscribeOn(Schedulers.io())
                .subscribe(
                    {
                        data.postValue(it)
                    },
                    { }
                )
        )
    }

    fun getGpx(track: Track, context: Context): Observable<GPXObservable> {
        return Single.fromCallable { dao.getLocations(track.id) }
            .subscribeOn(Schedulers.io())
            .flatMapObservable { points ->
                Observable.fromCallable {
                    try {
                        val gpx = createGpxFile(points)
                        val outputFile = File.createTempFile("peakery-${track.id}", ".gpx", context.cacheDir)
                        outputFile.deleteOnExit()
                        GPX.write(gpx, outputFile.path)
                        GPXObservable(
                            true,
                            outputFile
                        )
                    } catch (e: Error) {
                        e.printStackTrace()
                        GPXObservable(
                            false,
                            null
                        )
                    }
                }
                .subscribeOn(Schedulers.io())
            }
            .observeOn(AndroidSchedulers.mainThread())
    }

    fun getRequestedPermissions(): String {
        return Manifest.permission.WRITE_EXTERNAL_STORAGE
    }

    private fun createGpxFile(trackPoints: List<Point>): GPX? {
        return GPX.builder().addTrack { trackBuilder ->
            trackBuilder.addSegment { segment ->
                trackPoints.forEach { point ->
                    segment.addPoint { newPoint ->
                        newPoint.lat(point.latitude)
                        newPoint.lon(point.longitude)
                        newPoint.ele(point.elevation)
                        newPoint.time(point.time)
                    }
                }
            }
        }.build()
    }
}

data class HistoryData(
    val track: Track,
    val points: List<Point>
)