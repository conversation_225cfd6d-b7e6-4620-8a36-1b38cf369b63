package com.peakery.android.feature.tabs

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.PowerManager
import android.provider.Settings
import android.widget.ImageView
import androidx.lifecycle.Observer
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.firebase.analytics.FirebaseAnalytics
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.state.StateMachine
import com.peakery.android.feature.auth.AuthActivity
import com.peakery.android.snack
import com.tbruyelle.rxpermissions2.RxPermissions
import com.waseemsabir.betterypermissionhelper.BatteryPermissionHelper
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel


class TabsFragment: PeakeryFragment(R.layout.fragment_tabs) {

    private val model: TabsViewModel by viewModel()
    private val analytics: FirebaseAnalytics by inject()

    private lateinit var tab_map: ImageView
    private lateinit var tab_list: ImageView
    private lateinit var tab_recent: ImageView
    private lateinit var tab_profile: ImageView
    private lateinit var tab_record: ImageView
    private lateinit var permissions: RxPermissions

    companion object { fun new() : TabsFragment = TabsFragment() }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        permissions = RxPermissions(this)

        tab_map = requireView().findViewById(R.id.tab_map)
        tab_list = requireView().findViewById(R.id.tab_list)
        tab_recent = requireView().findViewById(R.id.tab_recent)
        tab_profile = requireView().findViewById(R.id.tab_profile)
        tab_record = requireView().findViewById(R.id.tab_record)

        model.state.observe(viewLifecycleOwner, Observer {
            when (it.state) {
                StateMachine.State.EXPLORE_MAP -> {
                    clearSelectedStates()
                    tab_map.isSelected = true
                }
                StateMachine.State.EXPLORE_LIST -> {
                    clearSelectedStates()
                    tab_list.isSelected = true
                }
                StateMachine.State.LATEST -> {
                    clearSelectedStates()
                    tab_recent.isSelected = true
                }
                StateMachine.State.PROFILE -> {
                    clearSelectedStates()
                    tab_profile.isSelected = true
                }

                else -> {} // ignore
            }
        })

        tab_record.setOnClickListener {
            if (model.isAuthenticated()) {
                if (isBatteryUnrestricted()) {
                    clearSelectedStates()
                    tab_record.isSelected = true
                    model.onRecordSelected()
                } else {
                    showBatteryRestrictionDialog()
                }
            } else {
                snack("You must sign-in to record a track")
                startActivity(AuthActivity.newIntent(requireContext()))
            }
            analytics.setCurrentScreen(requireActivity(), "log_tab", null)
            analytics.logEvent("click_log_tab", null)
        }
        tab_map.setOnClickListener {
            clearSelectedStates()
            tab_map.isSelected = true
            model.onMapSelected()
            analytics.setCurrentScreen(requireActivity(), "map_tab", null)
            analytics.logEvent("click_map_tab", null)
        }
        tab_list.setOnClickListener {
            clearSelectedStates()
            tab_list.isSelected = true
            model.onListSelected()
            analytics.setCurrentScreen(requireActivity(), "list_tab", null)
            analytics.logEvent("click_list_tab", null)
        }
        tab_recent.setOnClickListener {
            clearSelectedStates()
            tab_recent.isSelected = true
            model.onLatestSelected()
            analytics.setCurrentScreen(requireActivity(), "latest_tab", null)
            analytics.logEvent("click_latest_tab", null)
        }
        tab_profile.setOnClickListener {
            if (model.isAuthenticated()) {
                clearSelectedStates()
                tab_profile.isSelected = true
                model.onProfileSelected()
                analytics.setCurrentScreen(requireActivity(), "profile_tab", null)
                analytics.logEvent("click_profile_tab", null)
            } else {
                snack("You must sign-in to view your profile")
                startActivity(AuthActivity.newIntent(requireContext()))
            }
        }
    }

    /**
     * Android 12 introduced battery restrictions which can make it that tracking
     * doesn't work when the app is backgrounded despite the foreground service.
     *
     * This method checks if battery restrictions are in place so we can prompt users
     * to switch it to "Unrestricted" so tracking can work normally.
     */
    private fun isBatteryUnrestricted(): Boolean {
        val batteryPermissionHelper = BatteryPermissionHelper.getInstance()

// check whether or not Battery Permission is Available for Device
        val isBatteryPermissionAvailable = batteryPermissionHelper.isBatterySaverPermissionAvailable(context = requireContext(), onlyIfSupported = true)

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
            val powerManager = requireContext().getSystemService(Context.POWER_SERVICE) as PowerManager
            val packageName = requireContext().packageName
            return powerManager.isIgnoringBatteryOptimizations(packageName).not()
        }
        return true
    }

    /**
     * Android 12 introduced battery restrictions which can make it that tracking
     * doesn't work when the app is backgrounded despite the foreground service.
     *
     * This method prompts the user to disable battery restrictions in system settings
     */
    private fun showBatteryRestrictionDialog() {
        MaterialAlertDialogBuilder(requireActivity(), R.style.AlertDialogNeutral)
            .setTitle(R.string.disable_battery_optimizations)
            .setMessage(R.string.battery_optimizations_prompt)
            .setPositiveButton(R.string.battery_settings) { _, _ ->
                val intent = Intent()
                intent.setAction(Settings.ACTION_IGNORE_BATTERY_OPTIMIZATION_SETTINGS)
                requireContext().startActivity(intent)
            }
            .setNeutralButton(R.string.app_settings) {_, _ ->
                val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", requireContext().packageName, null)
                intent.setData(uri)
                startActivity(intent)
            }
            .create()
            .show()
    }


    private fun clearSelectedStates() {
        tab_map.isSelected = false
        tab_list.isSelected = false
        tab_recent.isSelected = false
        tab_profile.isSelected = false
    }
}
