package com.peakery.android.feature.logging.map

import com.peakery.android.core.model.Peak
import io.reactivex.subjects.BehaviorSubject

class PeakSelectionRepository {

    val selectedPeaks = BehaviorSubject.create<List<Peak>>()
    private val peaks = mutableListOf<Peak>()

    fun selectPeak(vararg peak: Peak) {
        peaks.addAll(peak)
        selectedPeaks.onNext(peaks)
    }

    fun unselectPeak(peak: Peak) {
        peaks.remove(peak)
        selectedPeaks.onNext(peaks)
    }

    fun clear() {
        peaks.clear()
        selectedPeaks.onNext(peaks)
    }
}