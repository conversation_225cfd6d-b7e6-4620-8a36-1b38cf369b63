package com.peakery.android.feature.settings

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.webkit.CookieManager
import android.webkit.CookieSyncManager
import androidx.preference.ListPreference
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import com.peakery.android.BuildConfig
import com.peakery.android.R
import com.peakery.android.feature.main.MainActivity
import com.peakery.android.feature.webview.WebViewActivity
import com.peakery.android.snack
import com.peakery.android.toast
import org.koin.androidx.viewmodel.ext.android.viewModel

class SettingsFragment: PreferenceFragmentCompat() {

    private val model: SettingsViewModel by viewModel()

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        setPreferencesFromResource(R.xml.settings, rootKey)

        val distancePreference = preferenceScreen.findPreference<ListPreference>("distance_unit")
        distancePreference!!.summary = model.getLenghtUnit()
        distancePreference.value = model.getLenghtUnit()
        distancePreference.setOnPreferenceChangeListener { preference, newValue ->
            model.setUserDistanceUnit(newValue.toString())
            preference.summary = newValue.toString()
            true
        }

        val appVersion = preferenceScreen.findPreference<Preference>("app_version")
        appVersion?.summary = model.getAppVersion()

        if (BuildConfig.APPLICATION_ID != "com.peakery.android") {
            val developerSettings = preferenceScreen.findPreference<Preference>("developer_settings")
            developerSettings?.isVisible = true
        }
    }

    override fun onPreferenceTreeClick(preference: Preference): Boolean {
        return when (preference.key) {
            "logout" -> {
                model.logout()
                snack("You are now logged out")
                val restartIntent = Intent(activity, MainActivity::class.java)
                    .addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

                CookieSyncManager.createInstance(requireContext())
                val cookieManager = CookieManager.getInstance()
                cookieManager.removeAllCookies { }

                startActivity(restartIntent)
                true
            }
            "terms" -> {
                startActivity(WebViewActivity.newIntent(requireContext(), model.getTermsUrl()))
                true
            }
            "privacy" -> {
                startActivity(WebViewActivity.newIntent(requireContext(), model.getPrivacyUrl()))
                true
            }
            "feedback" -> {
                val intent = Intent(Intent.ACTION_SEND)
                intent.type = "message/rfc822"
                intent.putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>"))
                intent.putExtra(Intent.EXTRA_SUBJECT, "Peakery Android Feedback")
                intent.putExtra(Intent.EXTRA_TEXT, "\n\n\n\nDEVICE INFO\n ${model.getDeviceInfo()}")

                startActivity(Intent.createChooser(intent, "Send Feedback"))
                true
            }
            "advanced" -> {
                AdvancedSettingsActivity.start(activity)
                true
            }
             "developer_settings" -> {
                DeveloperSettingsActivity.start(activity)
                true
            }
            else -> super.onPreferenceTreeClick(preference)
        }
    }
}
