package com.peakery.android.feature.map

import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.location.DebugLocation
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.metersToFeet
import com.peakery.android.numberFormat
import kotlin.math.roundToInt

class LocationDebugViewModel(tracker: Tracker,
                             private val userPreferences: UserPreferences): PeakeryViewModel() {

    val debugLocation = MutableLiveData<DebugLocation>()

    init {
        safeSubscribe(tracker.debugLocation.subscribe {
            debugLocation.postValue(it)
        })
    }

    fun getAltitudeForUserPreference(altitudeInMeters: Double): String {
        return when (userPreferences.getLengthUnit()) {
            LengthUnit.IMPERIAL -> altitudeInMeters.metersToFeet().numberFormat()
            LengthUnit.METRIC -> altitudeInMeters.roundToInt().numberFormat()
        }
    }
}
