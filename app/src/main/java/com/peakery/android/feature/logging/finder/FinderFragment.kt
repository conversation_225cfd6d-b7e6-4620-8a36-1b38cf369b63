package com.peakery.android.feature.logging.finder

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.nbsp.materialfilepicker.ui.FilePickerActivity
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.log
import com.peakery.android.okSnack
import com.tbruyelle.rxpermissions2.RxPermissions
import java.io.File

class FinderFragment: PeakeryFragment(R.layout.fragment_finder) {

    private lateinit var finder_list: RecyclerView
    private lateinit var finder_header: View

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        finder_list = requireView().findViewById(R.id.finder_list)
        finder_header = requireView().findViewById(R.id.finder_header)

        val adapter = FinderAdapter(requireContext())
        finder_list.adapter = adapter
        adapter.observable.observe(viewLifecycleOwner, Observer { file ->
            val data = Intent().apply {
                putExtra(FilePickerActivity.RESULT_FILE_PATH, file.path)
            }
            activity?.setResult(Activity.RESULT_OK, data)
            activity?.finish()
        })

        val permissions = RxPermissions(this)
        safeSubscribe(permissions.request(getReadStoragePermission()).subscribe(
            { granted ->
                if (granted) {
                    loadFiles(Environment.getExternalStorageDirectory().absoluteFile, adapter)
                } else {
                    okSnack("Files and media permission required")
                    finder_header.visibility = View.GONE
                }
            },
            {
                okSnack("Files and media permission required")
                finder_header.visibility = View.GONE
                it.log()
            }
        ))
    }

    private fun getReadStoragePermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
    }

    private fun loadFiles(dir: File, adapter: FinderAdapter) {
        val listFile = dir.listFiles()
        if (listFile != null && listFile.isNotEmpty()) {
            for (i in listFile.indices) {
                if (listFile[i].isDirectory) {
                    loadFiles(listFile[i], adapter)
                } else {
                    if (listFile[i].name.endsWith(".gpx")) {
                        adapter.add(listFile[i])
                        Log.e("Files", "FileName:${listFile[i].absolutePath}}")
                    }
                }
            }
        }
        finder_header.visibility = View.GONE
    }

}
