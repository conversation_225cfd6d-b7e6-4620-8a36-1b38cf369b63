package com.peakery.android.feature.logging

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.RadioGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.peakery.android.R
import com.peakery.android.core.model.Peak
import com.peakery.android.inflate

class LoggingPeakSelectionAdapter(private val model: LoggingViewModel) : RecyclerView.Adapter<LoggingPeakSelectionViewHoldel>() {

    private var peaks: List<Peak> = listOf()

    override fun getItemCount(): Int = peaks.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LoggingPeakSelectionViewHoldel {
        return LoggingPeakSelectionViewHoldel(parent.inflate(R.layout.item_selected_peak))
    }

    override fun onBindViewHolder(holder: LoggingPeakSelectionViewHoldel, position: Int) {
        val context = holder.name.context

        val peak = peaks[position]
        holder.name.text = peak.name

        val regionName = peak.region?: ""
        val countryName = peak.country?: ""
        holder.location.text = if (regionName.isNotEmpty()) {
            "$regionName, $countryName"
        } else { "" }

        val summits = context!!.resources.getQuantityString(R.plurals.summits, peak.summit_count, peak.summit_count)
        holder.info.text = "${model.getElevationText(peak.elevation)} • $summits"

        Glide.with(context)
            .load(peak.thumbnail_url)
            .transition(DrawableTransitionOptions.withCrossFade())
            .transform(CenterCrop(), RoundedCorners(context.resources.getDimensionPixelSize(R.dimen.radius_small)))
            .into(holder.image)

        holder.delete.setOnClickListener { model.unselectPeak(peak) }

        holder.radio.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.item_selected_peak_radio_summit -> { model.setSummited(peak.id) }
                R.id.item_selected_peak_radio_attempt -> { model.setAttempted(peak.id) }
            }
        }
    }

    fun update(peaks: List<Peak>) {
        this.peaks = peaks
        notifyDataSetChanged()
    }
}

class LoggingPeakSelectionViewHoldel(itemView: View): RecyclerView.ViewHolder(itemView) {
    val name: TextView = itemView.findViewById(R.id.item_selected_peak_name)
    val location: TextView = itemView.findViewById(R.id.item_selected_peak_location)
    val info: TextView = itemView.findViewById(R.id.item_selected_peak_extra_info)
    val image: ImageView = itemView.findViewById(R.id.item_selected_peak_image)
    val delete: View = itemView.findViewById(R.id.item_selected_peak_delete)
    val radio: RadioGroup = itemView.findViewById(R.id.item_selected_peak_radio)
}