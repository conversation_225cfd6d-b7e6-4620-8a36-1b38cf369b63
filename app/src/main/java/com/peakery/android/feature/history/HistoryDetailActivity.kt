package com.peakery.android.feature.history

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.EXTRA_TRACK_ID
import org.koin.androidx.viewmodel.ext.android.viewModel

class HistoryDetailActivity : AppCompatActivity() {

    private val model: HistoryDetailViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_history_detail)
        val history_detail_list = findViewById<RecyclerView>(R.id.history_detail_list)

        model.data.observe(this, Observer {
            history_detail_list.adapter = HistoryDetailAdapter(it)
        })

        val id = intent.extras!!.getLong(EXTRA_TRACK_ID)
        model.loadPoints(id)
    }
}
