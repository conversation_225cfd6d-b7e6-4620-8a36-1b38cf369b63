package com.peakery.android.feature.auth

import android.content.Context
import android.webkit.JavascriptInterface
import io.reactivex.subjects.PublishSubject

/**
 * Interfaces with <PERSON><PERSON> web page and intercepts Android.on...(...)
 */
class AuthInterface(private val context: Context) {

    val userIdObservable = PublishSubject.create<Int>()
    val userNameObservable = PublishSubject.create<String>()
    val userTokenObservable = PublishSubject.create<String>()

    @JavascriptInterface fun onAuthenticatedUserReady(id: Int) = userIdObservable.onNext(id)

    @JavascriptInterface fun onAuthenticatedUserNameReady(username: String) = userNameObservable.onNext(username)

    @JavascriptInterface fun onAuthenticatedTokenReady(token: String) = userTokenObservable.onNext(token)
}