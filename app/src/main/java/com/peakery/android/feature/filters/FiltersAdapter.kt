package com.peakery.android.feature.filters

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.inflate
import com.peakery.android.toYears
import io.apptik.widget.MultiSlider
import io.reactivex.disposables.CompositeDisposable
import java.text.DecimalFormat

private const val VIEW_TYPE_RANGE = 0
private const val VIEW_TYPE_SORT= 1
private const val VIEW_TYPE_SELECTION = 2

class FiltersAdapter(private val filters: List<Filter>,
                     private val model: FiltersViewModel): RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    var numberFormatter = DecimalFormat("#,###,###")
    private val disposables = CompositeDisposable()

    class SortViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
//        val title: TextView = itemView.findViewById(R.id.filter_item_select_title)
//        val segmentedControls: SegmentedControls = itemView.findViewById(R.id.filter_item_select_segments)
    }

    class RangeViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(R.id.filter_item_title)
        val subtitle: TextView = itemView.findViewById(R.id.filter_item_subtitle)
        val slider: MultiSlider = itemView.findViewById(R.id.filter_item_slider)
    }

    class SelectionViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(R.id.filter_item_title)
        val subtitle: TextView = itemView.findViewById(R.id.filter_item_subtitle)
        val slider: MultiSlider = itemView.findViewById(R.id.filter_item_slider)
    }

    override fun getItemCount(): Int = filters.size

    override fun getItemViewType(position: Int): Int {
        val filter = filters[position]
        return when (filter.type) {
            FilterType.RANGE -> VIEW_TYPE_RANGE
            FilterType.SORT -> VIEW_TYPE_SORT
            FilterType.SELECTION -> VIEW_TYPE_SELECTION
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_RANGE -> RangeViewHolder(parent.inflate(R.layout.item_filter_range))
            VIEW_TYPE_SORT -> SortViewHolder(parent.inflate(R.layout.item_filter_sort_empty))
            VIEW_TYPE_SELECTION -> SelectionViewHolder(parent.inflate(R.layout.item_filter_selection))
            else -> throw Error("Unknown type")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val filter = filters[position]
        when (filter.type) {
            FilterType.RANGE -> {
                holder as RangeViewHolder
                holder.title.text = filter.title
                holder.subtitle.text = getTextForFilter(filter, filter.selectedRange!!.min, filter.selectedRange.max)
                holder.slider.min = filter.defaultRange!!.min
                holder.slider.max = filter.defaultRange.max
                holder.slider.getThumb(0).value = filter.selectedRange.min
                holder.slider.getThumb(1).value = filter.selectedRange.max
                holder.slider.setOnThumbValueChangeListener { _, _, thumbIndex, value ->
                    // Ensure we don't deal with stale object- filter can be modified
                    val filter = filters[position]
                    if (thumbIndex == 0) { // Minimum
                        model.updateRangeFilter(filter, Range(value, filter.selectedRange!!.max))
                        holder.subtitle.text = getTextForFilter(filter, value, filter.selectedRange.max)
                    } else { // Maximum
                        model.updateRangeFilter(filter, Range(filter.selectedRange!!.min, value))
                        holder.subtitle.text = getTextForFilter(filter, filter.selectedRange.min, value)
                    }
                    setActiveFilterColors(filter, holder.title, holder.subtitle)
                }
                setActiveFilterColors(filter, holder.title, holder.subtitle)
            }
            FilterType.SORT -> {
//                holder as SortViewHolder
//                holder.title.text = filter.title
//                holder.segmentedControls.setLeftText(filter.imperialValues!![0].value.capitalize())
//                holder.segmentedControls.setRightText(filter.imperialValues!![1].value.capitalize())
//                disposables.add(holder.segmentedControls.observer.subscribe { segment ->
//                    model.updateSortFilter(filter, segment.position)
//                })
//                holder.segmentedControls.selectSegment(filter.value!!.name)
            }
            FilterType.SELECTION -> {
                val size = filter.values!!.size-1
                holder as SelectionViewHolder
                holder.title.text = filter.title
                holder.subtitle.text = getTextForFilter(filter, filter.selectedPosition!!)
                holder.slider.max = size
                holder.slider.getThumb(0).value = filter.selectedPosition
                holder.slider.setOnThumbValueChangeListener { _, _, _, value ->
                    // Ensure we don't deal with stale object- filter can be modified
                    val filter = filters[position]
                    model.updateSelectionFilter(filter, value)
                    holder.subtitle.text = getTextForFilter(filter, value)
                    setActiveFilterColors(filter, holder.title, holder.subtitle)
                }
                holder.slider.rotationY = 180f
                setActiveFilterColors(filter, holder.title, holder.subtitle)
            }
        }
    }

    private fun setActiveFilterColors(filter: Filter, title: TextView, subtitle: TextView) {
        val res = title.context.resources
        if (filter.isDefault()) {
            title.setTextColor(res.getColor(R.color.filter_color_default))
            subtitle.setTextColor(res.getColor(R.color.filter_color_default))
        } else {
            title.setTextColor(res.getColor(R.color.filter_color_active))
            subtitle.setTextColor(res.getColor(R.color.filter_color_active))
        }
    }

    fun tearDown() {
        disposables.clear()
    }

    private fun getTextForFilter(filter: Filter, minPosition: Int, maxPosition: Int = 0): String {
        return when (filter.value) {
            Value.DISTANCE -> "$minPosition to $maxPosition${getPlusOrEmpty(filter, maxPosition)} ${getLargeLengthUnit()}"
            Value.VERTICAL_GAIN -> getVerticalGainText(filter, minPosition, maxPosition)
            Value.POPULARITY -> getPopularityText(filter, minPosition, maxPosition)
            Value.ELEVATION -> getElevationText(filter, minPosition, maxPosition)
            Value.PROMINENCE -> getProminenceText(filter, minPosition, maxPosition)
            Value.LAST_CLIMBED -> getLastClimbedText(filter, minPosition)
            Value.DIFFICULTY -> "Class $minPosition to $maxPosition"
            else -> ""
        }
    }

    private fun getLastClimbedText(filter: Filter, position: Int): String {
        return when (val days = filter.values!![position]) {
            1 -> "Last day"
            in 2..90 -> "Last $days days"
            365 -> "Last year"
            in 91..99999 -> "Last ${days.toYears()} years"
            else -> "Any time"
        }
    }

    private fun getPopularityText(filter: Filter, minPosition: Int, maxPosition: Int): String {
        val minValue = filter.values!![minPosition]
        val maxValue = filter.values[maxPosition]
        return "$minValue to $maxValue${getPlusOrEmpty(filter, maxValue)} climbs"
    }

    private fun getVerticalGainText(filter: Filter, minPosition: Int, maxPosition: Int): String {
        val minValue = filter.values!![minPosition]
        val maxValue = filter.values[maxPosition]
        return "${numberFormatter.format(minValue)} to ${numberFormatter.format(maxValue)}${getPlusOrEmpty(filter, maxValue)} ${getSmallLengthUnit()}"
    }

    private fun getElevationText(filter: Filter, minPosition: Int, maxPosition: Int): String {
        val values = filter.values!!
        return "${numberFormatter.format(values[minPosition])} to ${numberFormatter.format(values[maxPosition])} ${getSmallLengthUnit()}"
    }

    private fun getProminenceText(filter: Filter, minPosition: Int, maxPosition: Int): String {
        val values = filter.values!!
        return "${numberFormatter.format(values[minPosition])} to ${numberFormatter.format(values[maxPosition])} ${getSmallLengthUnit()}"
    }

    private fun getSmallLengthUnit(): String {
        return when (model.getLengthUnit()) {
            LengthUnit.IMPERIAL -> "ft"
            LengthUnit.METRIC -> "m"
        }
    }

    private fun getLargeLengthUnit(): String {
        return when (model.getLengthUnit()) {
            LengthUnit.IMPERIAL -> "miles"
            LengthUnit.METRIC -> "km"
        }
    }

    /**
     * Return + if value is the same as the filter's max value
     */
    private fun getPlusOrEmpty(filter: Filter, maxValue: Int): String {
        val values = filter.values
        return if (values != null && values.last() == maxValue) {
            "+"
        } else if (maxValue == filter.defaultRange!!.max) {
            "+"
        } else {
            ""
        }
    }
}
