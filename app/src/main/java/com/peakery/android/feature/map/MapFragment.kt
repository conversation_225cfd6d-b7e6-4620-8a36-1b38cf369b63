package com.peakery.android.feature.map

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.BitmapFactory
import android.graphics.Color
import android.location.Location
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.ImageButton
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.google.gson.JsonParser
import com.jakewharton.rxbinding2.view.RxView
import com.mapbox.android.gestures.MoveGestureDetector
import com.mapbox.mapboxsdk.Mapbox
import com.mapbox.mapboxsdk.camera.CameraPosition
import com.mapbox.mapboxsdk.camera.CameraUpdateFactory
import com.mapbox.mapboxsdk.constants.MapboxConstants
import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.mapbox.mapboxsdk.location.LocationComponentActivationOptions
import com.mapbox.mapboxsdk.location.LocationComponentOptions
import com.mapbox.mapboxsdk.location.modes.CameraMode
import com.mapbox.mapboxsdk.maps.MapView
import com.mapbox.mapboxsdk.maps.MapboxMap
import com.mapbox.mapboxsdk.maps.Style
import com.mapbox.mapboxsdk.plugins.annotation.Line
import com.mapbox.mapboxsdk.plugins.annotation.LineManager
import com.mapbox.mapboxsdk.plugins.annotation.LineOptions
import com.mapbox.mapboxsdk.plugins.annotation.OnSymbolClickListener
import com.mapbox.mapboxsdk.plugins.annotation.Symbol
import com.mapbox.mapboxsdk.plugins.annotation.SymbolManager
import com.mapbox.mapboxsdk.plugins.annotation.SymbolOptions
import com.mapbox.mapboxsdk.style.layers.RasterLayer
import com.mapbox.mapboxsdk.style.sources.RasterSource
import com.mapbox.mapboxsdk.style.sources.TileSet
import com.mapbox.mapboxsdk.utils.ColorUtils
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.model.*
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.ui.MapButtons
import com.peakery.android.feature.layers.Layer
import com.peakery.android.feature.layers.LayersFragment
import com.peakery.android.feature.main.UiCoordinator
import com.peakery.android.snack
import com.peakery.android.toLatLng
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel
import java.util.*
import java.util.concurrent.TimeUnit

private const val ICON_PEAK_BLUE = "peak_blue"
private const val ICON_PEAK_GREEN = "peak_green"
private const val ICON_PEAK_ORANGE = "peak_orange"
private const val ICON_PEAK_RED = "peak_red"
private const val ICON_PEAK_YELLOW = "peak_yellow"

class MapFragment: PeakeryFragment(R.layout.fragment_map) {

    companion object { fun new() : MapFragment = MapFragment() }

    private val model: MapViewModel by sharedViewModel()
    private val uiCoordinator: UiCoordinator by inject()

    private lateinit var map: MapboxMap
    private lateinit var style: Style
    private lateinit var lineManager: LineManager

    private lateinit var symbolManager: SymbolManager
    private lateinit var highestSymbolManager: SymbolManager
    private lateinit var selectedSymbolManager: SymbolManager

    private var trackline: Line? = null
    private var lastSelectedSymbol: Symbol? = null
    private var mapPeakFragment: Fragment? = null
    private val mapMoveHandler = Handler(Looper.getMainLooper())
    private lateinit var lastBounds: LatLngBounds

    private var initialMapResultsQueried = false
    private var initialLocationCentered = false

    private var lastCameraPosition: CameraPosition? = null
    private var lastZoomLevel = 0.0
    private var lastTarget = LatLng(0.0, 0.0)

    private val symbolDeque: Deque<ArrayList<Symbol>> = LinkedList()

    private lateinit var main_map: MapView
    private lateinit var map_buttons: MapButtons
    private lateinit var map_button_location: ImageButton
    private lateinit var map_button_layers: ImageButton
    private lateinit var map_peak_fragment_container: ViewGroup

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        main_map = requireView().findViewById(R.id.main_map)
        map_buttons = requireView().findViewById(R.id.map_buttons)
        map_button_location = map_buttons.findViewById(R.id.map_button_location)
        map_button_layers = map_buttons.findViewById(R.id.map_button_layers )
        map_peak_fragment_container = requireView().findViewById(R.id.map_peak_fragment_container)

        initMap(savedInstanceState)
        initButtons()
    }

    private fun initMap(savedInstanceState: Bundle?) {
        main_map.onCreate(savedInstanceState)

        main_map.getMapAsync {
            this.map = it

            map.uiSettings.isCompassEnabled = false
            map.uiSettings.isRotateGesturesEnabled = false
            map.uiSettings.isTiltGesturesEnabled = false

            map.setStyle(Style.OUTDOORS) { style -> initStyle(style) }
        }
    }

    private fun initStyle(style: Style) {
        updateStyle(style)
        this.lineManager = LineManager(main_map, map, style, "com.mapbox.annotations.points") // ensure line is under location

        enableLocationComponent()

        // mapbox seems to fail to change layer right away in activities, causing
        // callback failures later on. adding a delay fixes that
        Handler().postDelayed({ initMapObservers() }, 200)

        map.gesturesManager.moveGestureDetector.moveThreshold = 3f
        map.addOnMoveListener(object: MapboxMap.OnMoveListener {
            override fun onMove(detector: MoveGestureDetector) {}
            override fun onMoveEnd(detector: MoveGestureDetector) {}

            override fun onMoveBegin(detector: MoveGestureDetector) {
                unselectPeak()
                disableCameraTracking()
                lastZoomLevel = map.cameraPosition.zoom
                lastTarget = map.cameraPosition.target
            }
        })

        map.addOnCameraIdleListener {
            toggleSymbolOverlap()
            val newZoomLevel = map.cameraPosition.zoom
            val newTarget = map.cameraPosition.target
            val distanceThreshold = when {
                newZoomLevel < 12 -> 1000 // 1km
                newZoomLevel < 14 -> 300 // 300m
                newZoomLevel < 17 -> 20// 20m
                newZoomLevel < 22 -> 2// 2m
                else -> 1 // 1m
            }
            if (!initialMapResultsQueried || newZoomLevel != lastZoomLevel || lastTarget.distanceTo(newTarget) > distanceThreshold) {
                updateMapBounds()
            }

            lastZoomLevel = newZoomLevel
            lastTarget = newTarget
        }

        map.addOnMapClickListener {
            if (mapPeakFragment != null) {
                unselectPeak()
                true
            }
            hideKeyboard()
            false
        }

        ViewCompat.setOnApplyWindowInsetsListener(requireView()) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())

            v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                bottomMargin = insets.bottom
            }
            map.setPadding(
                resources.getDimensionPixelSize(R.dimen.map_padding_left_right),
                resources.getDimensionPixelSize(R.dimen.map_padding_top_bottom),
                resources.getDimensionPixelSize(R.dimen.map_padding_left_right),
                resources.getDimensionPixelSize(R.dimen.map_padding_top_bottom)
            )
            WindowInsetsCompat.CONSUMED
        }

    }

    private fun disableCameraTracking() {
        map.locationComponent.cameraMode = CameraMode.NONE
        map_button_location.setColorFilter(resources.getColor(R.color.dark_grey))
    }

    private fun enableCameraTracking() {
        map.locationComponent.cameraMode = CameraMode.TRACKING
        map_button_location.setColorFilter(resources.getColor(R.color.colorAccent))
    }

    private fun unselectPeak() {
        val transaction = parentFragmentManager.beginTransaction()
        transaction.setCustomAnimations(R.anim.slide_in_up, R.anim.slide_in_down, R.anim.slide_out_down, R.anim.slide_out_up)
        transaction.replace(R.id.map_peak_fragment_container, EmptyMapPeakFragment()).commit()
        mapPeakFragment = null
        resetLastSelectedSymbol()
        map_buttons.visibility = View.VISIBLE
    }

    private fun updateStyle(style: Style) {
        symbolDeque.clear()
        this.style = style
        if (::symbolManager.isInitialized) symbolManager.annotations.clear()
        if (::highestSymbolManager.isInitialized) highestSymbolManager.annotations.clear()
        if (::selectedSymbolManager.isInitialized) selectedSymbolManager.annotations.clear()

        symbolManager = SymbolManager(main_map, map, style)
        highestSymbolManager = SymbolManager(main_map, map, style)
        selectedSymbolManager = SymbolManager(main_map, map, style)

        toggleSymbolOverlap()

        symbolManager.iconPadding = 5f
        highestSymbolManager.iconPadding = 5f
        selectedSymbolManager.iconPadding = 5f

        style.addImage(ICON_PEAK_BLUE, BitmapFactory.decodeResource(resources, R.drawable.peak_blue))
        style.addImage(ICON_PEAK_GREEN, BitmapFactory.decodeResource(resources, R.drawable.peak_green))
        style.addImage(ICON_PEAK_ORANGE, BitmapFactory.decodeResource(resources, R.drawable.peak_orange))
        style.addImage(ICON_PEAK_RED, BitmapFactory.decodeResource(resources, R.drawable.peak_red))
        style.addImage(ICON_PEAK_YELLOW, BitmapFactory.decodeResource(resources, R.drawable.peak_orange))

        val symbolClickListener = OnSymbolClickListener {
            model.onTapPeakOnMap(it.getPeakId(), it.latLng)
            selectSymbol(it)
            true
        }

        symbolManager.addClickListener(symbolClickListener)
        highestSymbolManager.addClickListener(symbolClickListener)

        updateMapSymbols(model.peaks)
    }

    private fun toggleSymbolOverlap() {
        val allowOverlap = true // map.cameraPosition.zoom >= 12
        symbolManager.iconAllowOverlap = allowOverlap
        symbolManager.textAllowOverlap = allowOverlap
    }

    private fun selectSymbol(symbol: Symbol) {
        if (mapPeakFragment == null) {
            mapPeakFragment = MapPeakFragment()
            val transaction = parentFragmentManager.beginTransaction()
            transaction.setCustomAnimations(
                R.anim.slide_in_up,
                R.anim.slide_in_down,
                R.anim.slide_out_down,
                R.anim.slide_out_up
            )
            transaction.replace(R.id.map_peak_fragment_container, mapPeakFragment!!).commit()
        }
        resetLastSelectedSymbol()

        val selectedSymbolOptions = SymbolOptions()
            .withLatLng(symbol.latLng)
            .withIconImage(ICON_PEAK_ORANGE)
        val selectedSymbol = selectedSymbolManager.create(selectedSymbolOptions)

        lastSelectedSymbol = selectedSymbol
        map_buttons.visibility = View.GONE
    }

    private fun resetLastSelectedSymbol() {
        if (::selectedSymbolManager.isInitialized) {
            selectedSymbolManager.deleteAll()
        }
        lastSelectedSymbol = null
    }

    private val locationAnimationCallback = object: MapboxMap.CancelableCallback {
        override fun onFinish() { enableCameraTracking() }
        override fun onCancel() {  enableCameraTracking() }
    }

    private fun getStandardMapAnimationCallback(@CameraMode.Mode initialCameraMode: Int): MapboxMap.CancelableCallback {
        return object: MapboxMap.CancelableCallback {
            override fun onFinish() { if (initialCameraMode == CameraMode.TRACKING) enableCameraTracking() }
            override fun onCancel() { if (initialCameraMode == CameraMode.TRACKING) enableCameraTracking() }
        }
    }

    private fun centerUserLocation(location: Location?) {
        unselectPeak()
        if (location != null) {
            disableCameraTracking()

            if (model.state.value?.state == StateMachine.State.TRACKING) {
                map.animateCamera(CameraUpdateFactory.newLatLngZoom(location.toLatLng(), 15.0), locationAnimationCallback)
            } else {
                map.animateCamera(CameraUpdateFactory.newLatLngZoom(location.toLatLng(), 13.0), locationAnimationCallback)
            }
        }
    }

    @SuppressLint("CheckResult")
    private fun initButtons() {
        map_button_location.setOnClickListener {
            centerUserLocation(model.lastKnownLocation())
        }

        RxView.clicks(map_button_layers)
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe {
                val layersFragment = LayersFragment()
                layersFragment.show(parentFragmentManager, layersFragment.tag)
            }
    }

    private fun setLayer(layer: Layer) {
        when (layer.sourceId) {
            "terrain" -> map.setStyle(Style.Builder().fromUrl(model.getTerrainUrl())) { updateStyle(it) }
            "satellite" -> map.setStyle(Style.Builder().fromUrl(model.getSatelliteUrl())) { updateStyle(it) }
            "satellite-topo" -> map.setStyle(Style.Builder().fromUrl(model.getSatelliteTopoUrl())) { updateStyle(it) }
            else -> {
                map.setStyle(Style.OUTDOORS) {
                    updateStyle(it)
                    val customTiles = RasterSource(
                        layer.sourceId,
                        TileSet("raster", *layer.tileUrl),
                        256
                    )
                    style.addSource(customTiles)
                    style.addLayerBelow(RasterLayer(layer.layerId, layer.sourceId), MapboxConstants.LAYER_ID_ANNOTATIONS)
                }
            }
        }
    }

    private fun clearMapLayers() {
        if (style.isFullyLoaded.not()) return
        style.sources.forEach { source ->
            model.allLayers().forEach {
                if (source.id == it.sourceId) { style.removeSource(source) }
            }
        }
        style.layers.forEach { layer ->
            model.allLayers().forEach {
                if (layer.id == it.layerId) { style.removeLayer(layer) }
            }
        }
    }

    @SuppressLint("MissingPermission")
    private fun enableLocationComponent() {
        val locationComponentOptions = LocationComponentOptions.builder(requireContext())
            .minZoomIconScale(1.15f)
            .minZoomIconScale(1.15f)
//            .compassAnimationEnabled(true)
            .build()

        val locationComponentActivationOptions = LocationComponentActivationOptions
            .builder(requireContext(), style)
            .locationComponentOptions(locationComponentOptions)
            .build()

        map.locationComponent.activateLocationComponent(locationComponentActivationOptions)
        map.locationComponent.isLocationComponentEnabled = true
//        map.locationComponent.renderMode = RenderMode.COMPASS

        enableCameraTracking()
    }

    private fun initMapObservers() {
        model.layer.observe(viewLifecycleOwner, Observer {
            clearMapLayers()
            setLayer(it)
        })
        model.userPosition.observe(viewLifecycleOwner, Observer {
            updateUserLocation(it)
        })
        model.track.observe(viewLifecycleOwner, Observer {
            if (trackline == null) {
                trackline = lineManager.create(
                    LineOptions().withLatLngs(it.latLngs)
                        .withLineWidth(5.0f)
                        .withLineColor(ColorUtils.colorToRgbaString(Color.parseColor("#2257FF"))))
            } else {
                trackline!!.latLngs = it.latLngs
            }
            lineManager.update(trackline)
        })
        model.trackCompleted.observe(viewLifecycleOwner, Observer {
            lineManager.delete(trackline)
            trackline = null

            if (!it.isCompleted) {
                trackline = lineManager.create(
                    LineOptions().withLatLngs(it.latLngs)
                        .withLineWidth(5.0f)
                        .withLineColor(ColorUtils.colorToRgbaString(Color.parseColor("#2257FF"))))
            }
        })

        model.peaksDiscovery.observe(viewLifecycleOwner, Observer { peaks ->
            updateMapSymbols(peaks)
        })
        model.search.observe(viewLifecycleOwner, Observer { search ->
            unselectPeak()
            disableCameraTracking()

            val animationCallback = getStandardMapAnimationCallback(map.locationComponent.cameraMode)
            disableCameraTracking()
            when {
                search.isPeak -> {
                    map.animateCamera(CameraUpdateFactory.newLatLngZoom(search.latLng, 13.0), map.locationComponent.cameraMode)
                    addSelectedMapSymbol(search.latLng)
                }
                search.isCity -> map.animateCamera(CameraUpdateFactory.newLatLngZoom(search.latLng, 9.0), animationCallback)
                search.isRegion -> map.animateCamera(CameraUpdateFactory.newLatLngZoom(search.latLng, 6.0), animationCallback)
                else -> map.animateCamera(CameraUpdateFactory.newLatLngZoom(search.latLng, 4.0), animationCallback)
            }
        })

        model.webviewMapSelection.observe(viewLifecycleOwner, Observer {
            unselectPeak()
            map.animateCamera(CameraUpdateFactory.newLatLngZoom(it, 13.0),
                              getStandardMapAnimationCallback(map.locationComponent.cameraMode))
            addSelectedMapSymbol(it)
        })
        model.state.observe(viewLifecycleOwner, Observer { stateUpdate ->

            val animationCallback = getStandardMapAnimationCallback(map.locationComponent.cameraMode)
            disableCameraTracking()

            if (stateUpdate.state == StateMachine.State.TRACKING) {
                unselectPeak()
                lastCameraPosition = map.cameraPosition
                val lastLocation = model.lastKnownLocation()
                if (lastLocation != null) {
                    map.animateCamera(CameraUpdateFactory.newLatLngZoom(lastLocation.toLatLng(), 15.0), animationCallback)
                } else {
                    val lastTrackPoint = model.getLastTrackPoint()
                    if (lastTrackPoint != null) {
                        map.animateCamera(CameraUpdateFactory.newLatLngZoom(lastTrackPoint, 15.0), animationCallback)
                    }
                }
            } else {
                if (lastCameraPosition != null) {
                    map.animateCamera(CameraUpdateFactory.newLatLngZoom(lastCameraPosition!!.target, lastCameraPosition!!.zoom), animationCallback)
                    lastCameraPosition = null
                }
            }
        })
        uiCoordinator.bottomContentHeight.observe(viewLifecycleOwner, Observer {
            map_peak_fragment_container.translationY = -it

        })
        model.tracking.observe(viewLifecycleOwner, Observer {
            when (it) {
                Tracker.TrackingState.POST_TRACKING -> map_buttons.visibility = View.GONE
                else -> map_buttons.visibility = View.VISIBLE
            }
        })
        model.locationNotFound.observe(viewLifecycleOwner, Observer {
            it.startResolutionForResult(activity as Activity, 42)
        })
        model.filtersChange.observe(viewLifecycleOwner, Observer {
            symbolDeque.clear()
            symbolManager.deleteAll()
            unselectPeak()
        })
        model.noPeaksResults.observe(viewLifecycleOwner, Observer {
            snack("No peaks found")
        })
        model.showDebugLocations.observe(viewLifecycleOwner, Observer {  enabled ->
            if (enabled) {
                childFragmentManager.beginTransaction().add(R.id.map_fragment_debug_location, LocationDebugFragment()).commit()
            }
        })
    }

    private fun updateMapSymbols(peaks: List<Peak>) {
        if (symbolDeque.size >= 2) {
            symbolManager.delete(symbolDeque.removeLast())
        }
        val newSymbols = arrayListOf<Symbol>()

        // highest
        highestSymbolManager.deleteAll()
        val tallestPeak = peaks.filter { it.latLng() != lastSelectedSymbol?.latLng }.maxByOrNull { it.elevation }

        // all peaks
        peaks.forEach { peak ->
                 if (peak == tallestPeak) return@forEach

                 val symbolOptions = SymbolOptions()
                    .withPeakId(peak.id)
                    .withLatLng(peak.latLng())

                 if (peak.your_summits > 0) {
                     symbolOptions.withIconImage(ICON_PEAK_GREEN)
                 } else {
                     symbolOptions.withIconImage(ICON_PEAK_BLUE)
                 }

                 // Clear duplicate symbol from preview deque
                 if (symbolDeque.size > 0) {
                     val duplicateSymbol = symbolDeque.first?.find { it.latLng == peak.latLng() }
                     if (duplicateSymbol != null) {
                         symbolManager.delete(duplicateSymbol)
                         symbolDeque.first.remove(duplicateSymbol)
                     }
                 }
                 newSymbols.add(symbolManager.create(symbolOptions))
             }

        if (tallestPeak != null) {
            val symbolOptions = SymbolOptions()
                .withPeakId(tallestPeak.id)
                .withLatLng(tallestPeak.latLng())
                .withIconImage(ICON_PEAK_RED)

            highestSymbolManager.create(symbolOptions)
        }

        if (lastSelectedSymbol != null) {
            selectSymbol(lastSelectedSymbol!!)
        }
        symbolDeque.addFirst(newSymbols)
    }

    private fun addSelectedMapSymbol(latLng: LatLng) {
        val symbolOptions = SymbolOptions()
            .withLatLng(latLng)
            .withIconImage(ICON_PEAK_ORANGE)
        val symbol = selectedSymbolManager.create(symbolOptions)
        selectSymbol(symbol)
    }

    private fun updateUserLocation(location: Location?) {
        if (location == null) {
            return
        }
        if (!initialLocationCentered) {
            centerUserLocation(location)
            initialLocationCentered = true
        }
        map.locationComponent.forceLocationUpdate(location)
    }

    private fun updateMapBounds() {
        mapMoveHandler.removeCallbacksAndMessages(boundsRunnable)
        lastBounds = map.projection.getVisibleRegion(false).latLngBounds
        mapMoveHandler.postDelayed(boundsRunnable, 500)
        model.updateMapCenter(lastBounds.center)
    }

    private val boundsRunnable = Runnable {
        Log.e("mapfragment", "boundsRunnable")
        model.updateMapBounds(lastBounds)
        initialMapResultsQueried = true
    }

    /**
     * Mapbox
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Mapbox.getInstance(requireContext(), getString(R.string.access_token))
    }

    override fun onResume() {
        super.onResume()
        main_map.onResume()
    }

    override fun onStart() {
        super.onStart()
        main_map.onStart()
    }

    override fun onPause() {
        super.onPause()
        main_map.onPause()
    }

    override fun onStop() {
        super.onStop()
        main_map.onStop()
    }

    override fun onDestroy() {
        if (::symbolManager.isInitialized && symbolManager != null) {
            symbolManager.onDestroy()
        }
        super.onDestroy()
        main_map?.onDestroy()
    }

    override fun onLowMemory() {
        super.onLowMemory()
        main_map.onLowMemory()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        main_map.onDestroy()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        main_map.onSaveInstanceState(outState)
    }

    private fun hideKeyboard() {
        val view = requireActivity().findViewById<View>(android.R.id.content)
        if (view != null) {
            val imm = requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }

    private val jsonParser = JsonParser()
    private fun SymbolOptions.withPeakId(id: String): SymbolOptions {
        return this.withData(jsonParser.parse(id))
    }
    private fun Symbol.getPeakId(): String? {
        return this.data?.asString
    }
}
