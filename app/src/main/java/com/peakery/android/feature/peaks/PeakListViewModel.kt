package com.peakery.android.feature.peaks

import android.annotation.SuppressLint
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import com.peakery.android.core.model.Peak
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.filters.Filter
import com.peakery.android.feature.filters.Value
import com.peakery.android.log

@SuppressLint("CheckResult")
class PeakListViewModel(private val peakRepository: PeakRepository,
                        private val unitManager: UserPreferences): ViewModel() {

    var peakResults = MutableLiveData<List<Peak>>()
    var peaksLoading = MutableLiveData<Boolean>()
    val sortFilter = MutableLiveData<Filter?>()

    private var sort: Filter? = null

    init {
        peakRepository.peakResults.subscribe(
            { peakResults.postValue(it.body()!!.data.peaks) },
            { it.log() }
        )
        peakRepository.peaksLoading.subscribe {
            peaksLoading.postValue(it)
        }
        peakRepository.filtersObservable.subscribe { filters ->
            sort = filters.find { it.id == "sort" }
            if (sort != null) {
                sortFilter.postValue(sort)
            }
        }
    }

    fun getElevationText(elevation: Double): String {
        return unitManager.getSmallUnitFromFeet(elevation)
    }

    fun selectSort(value: Value) {
        sort?.let {
            sort = Filter(
                it.id,
                it.type,
                value,
                it.values,
                it.title,
                it.subtitle,
                it.defaultRange,
                it.selectedRange,
                it.selectedPosition
            )

            peakRepository.updateFilter(sort!!)
        }
    }
}