package com.peakery.android.feature.filters

val imperialStepsVerticalGain = listOf(
    0, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000
)

val metricStepsVerticalGain = listOf(
    0, 500, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000
)

val stepsPopularity = listOf(
    0, 1, 2, 3, 4, 5, 10, 20, 30, 40, 50, 100, 200, 300, 400, 500
)

val stepsLastClimbed = listOf(
    1, 2, 3, 7, 30, 60, 90, 365, 730, 1825, 3650, 0
)

val imperialStepsElevation = listOf(
    0, 500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500,
    10000, 10500, 11000, 11500, 12000, 12500, 13000, 13500, 14000, 14500, 15000, 15500, 16000, 16500, 17000, 17500, 18000, 18500, 19000, 19500,
    20000, 20500, 21000, 21500, 22000, 22500, 23000, 23500, 24000, 24500, 25000, 25500, 26000, 26500, 27000, 27500, 28000, 28500, 29000, 29500
)

val metricStepsElevation = listOf(
    0, 200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000, 2200, 2400, 2600, 2800, 3000, 3200, 3400, 3600, 3800, 4000, 4200, 4400, 4600, 4800,
    5000, 5200, 5400, 5600, 5800, 6000, 6200, 6400, 6600, 6800, 7000, 7200, 7400, 7600, 7800, 8000, 8200, 8400, 8600, 8800, 9000
)

val imperialStepsProminence= listOf(
    0, 30, 50, 100, 200, 300, 400, 500, 1000, 1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000, 6500, 7000, 7500, 8000, 8500, 9000, 9500,
    10000, 10500, 11000, 11500, 12000, 12500, 13000, 13500, 14000, 14500, 15000, 15500, 16000, 16500, 17000, 17500, 18000, 18500, 19000, 19500,
    20000, 20500, 21000, 21500, 22000, 22500, 23000, 23500, 24000, 24500, 25000, 25500, 26000, 26500, 27000, 27500, 28000, 28500, 29000, 29500
)

val metricStepsProminence= listOf(
    0, 10, 20, 30, 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200, 1300, 1400, 1500, 1600, 1700, 1800, 1900, 2000, 2100, 2200,
    2300, 2400, 2500, 2600, 2700, 2800, 2900, 3000, 3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000, 4100, 4200, 4300, 4400, 4500, 4600,
    4700, 4800, 4900, 5000, 5100, 5200, 5300, 5400, 5500, 5600, 5700, 5800, 5900, 6000, 6100, 6200, 6300, 6400, 6500, 6600, 6700, 6800, 6900, 7000,
    7100, 7200, 7300, 7400, 7500, 7600, 7700, 7800, 7900, 8000, 8100, 8200, 8300, 8400, 8500, 8600, 8700, 8800, 8900, 9000
)

val imperialFilters = listOf(
    Filter("distance", FilterType.RANGE, Value.DISTANCE, null,  "Length", "0 to 20+ miles", Range(0, 20), Range(0, 20)),
    Filter("vertical_gain", FilterType.RANGE, Value.VERTICAL_GAIN, imperialStepsVerticalGain, "Vertical gain", "0 to 10,000+ ft", Range(0, imperialStepsVerticalGain.size-1), Range(0, imperialStepsVerticalGain.size-1)),
    Filter("popularity", FilterType.RANGE, Value.POPULARITY, stepsPopularity,  "Popularity", "0 to 500+ climbs", Range(0, stepsPopularity.size-1), Range(0, stepsPopularity.size-1)),
    Filter("last_climbed", FilterType.SELECTION, Value.LAST_CLIMBED, stepsLastClimbed, "Last Climbed", "Any time", null, null, stepsLastClimbed.size-1),
    Filter("elevation", FilterType.RANGE, Value.ELEVATION, imperialStepsElevation,  "Elevation", "0 to 29,500 ft", Range(0, imperialStepsElevation.size-1), Range(0, imperialStepsElevation.size-1)),
    Filter("prominence", FilterType.RANGE, Value.PROMINENCE, imperialStepsProminence, "Prominence", "0 to 29,500 ft", Range(0, imperialStepsProminence.size-1), Range(0, imperialStepsProminence.size-1)),
    Filter("difficulty", FilterType.RANGE, Value.DIFFICULTY, null, "Difficulty", "Class 1 to 5", Range(1, 5), Range(1, 5)),
    Filter("sort", FilterType.SORT, Value.POPULAR, null, "Sort by")
)

val metricFilters = listOf(
    Filter("distance", FilterType.RANGE, Value.DISTANCE, null, "Length", "0 to 30+ km", Range(0, 30), Range(0, 30)),
    Filter("vertical_gain", FilterType.RANGE, Value.VERTICAL_GAIN, metricStepsVerticalGain,"Vertical gain", "0 to 3,000+ m", Range(0, metricStepsVerticalGain.size-1), Range(0, metricStepsVerticalGain.size-1)),
    Filter("popularity", FilterType.RANGE, Value.POPULARITY, stepsPopularity, "Popularity", "0 to 500+ climbs", Range(0, stepsPopularity.size-1), Range(0, stepsPopularity.size-1)),
    Filter("last_climbed", FilterType.SELECTION, Value.LAST_CLIMBED, stepsLastClimbed, "Last Climbed", "Any time", null, null, stepsLastClimbed.size-1),
    Filter("elevation", FilterType.RANGE, Value.ELEVATION, metricStepsElevation, "Elevation", "0 to 9,000 m", Range(0, metricStepsElevation.size-1), Range(0, metricStepsElevation.size-1)),
    Filter("prominence", FilterType.RANGE, Value.PROMINENCE, metricStepsProminence, "Prominence", "0 to 9,000 m", Range(0, metricStepsProminence.size-1), Range(0, metricStepsProminence.size-1)),
    Filter("difficulty", FilterType.RANGE, Value.DIFFICULTY, null,  "Difficulty", "Class 1 to 5", Range(1, 5), Range(1, 5)),
    Filter("sort", FilterType.SORT, Value.POPULAR, null, "Sort by")
)

data class Filter(
    val id: String,
    val type: FilterType,
    val value: Value? = null,
    val values: List<Int>? = null,
    val title: String? = null,
    val subtitle: String? = null,
    val defaultRange: Range? = null,
    val selectedRange: Range? = null,
    val selectedPosition: Int? = null
) {

    fun getMinValue(): Int {
        return if (values != null) {
            values[selectedRange!!.min]
        } else {
            selectedRange!!.min
        }
    }

    fun getMaxValue(): Int {
        return if (values != null) {
            values[selectedRange!!.max]
        } else {
            selectedRange!!.max
        }
    }

    fun isMaxDefault(): Boolean {
        return selectedRange?.max == defaultRange?.max
    }

    fun isMinDefault(): Boolean {
        return selectedRange?.min == defaultRange?.min
    }

    fun isDefault(): Boolean {
        return when (type) {
            FilterType.RANGE -> isMaxDefault() && isMinDefault()
            FilterType.SELECTION -> selectedPosition == values!!.size-1
            else -> false
        }
    }
}

data class Range(
    val min: Int,
    val max: Int
)

enum class Value(val value: String) {
    DISTANCE("distance"),
    VERTICAL_GAIN("vertical_gain"),
    POPULARITY("popularity"),
    ELEVATION("elevation"),
    PROMINENCE("prominence"),
    LAST_CLIMBED("last_climbed"),
    DIFFICULTY("difficulty"),
    NEARBY("nearby"),
    POPULAR("popular"),
    HIGHEST("elevation"),
}

enum class FilterType {
    RANGE,
    SORT,
    SELECTION
}