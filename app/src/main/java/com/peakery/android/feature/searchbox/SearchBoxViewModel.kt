package com.peakery.android.feature.searchbox

import android.annotation.SuppressLint
import androidx.lifecycle.MutableLiveData
import com.mapbox.mapboxsdk.geometry.LatLng
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.filters.Filter
import com.peakery.android.feature.map.MapChanges
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

@SuppressLint("CheckResult")
class SearchBoxViewModel(private val peakRepository: PeakRepository,
                         private val mapChanges: MapChanges,
                         private val unitManager: UserPreferences): PeakeryViewModel() {

    val suggestionsResults = MutableLiveData<List<Suggestion>>()
    val suggestionsLoading = MutableLiveData<Boolean>()
    val selectedSuggestion = MutableLiveData<Suggestion>()
    val peaksLoading = MutableLiveData<Boolean>()
    val mapCenterChange = MutableLiveData<LatLng>()

    val defaultFilters = MutableLiveData<Boolean>()
    val activeFilters = MutableLiveData<List<Filter>>()

    init {
        peakRepository.filtersObservable.subscribe {
            defaultFilters.postValue(peakRepository.areFiltersModified(true))
            activeFilters.postValue(peakRepository.getModifiedFilters(true))
        }
        peakRepository.searchSelection.subscribe { selectedSuggestion.postValue(it) }
        peakRepository.peaksLoading.subscribe { peaksLoading.postValue(it) }
        mapChanges.mapCenterObservable.subscribe { mapCenterChange.postValue(it) }
    }

    /**
     * Switchmap doesn't seem to do what it is intended (cancel the previous request)
     */
    fun search(keywords: String) {
        if (keywords.isEmpty()) {
            suggestionsResults.postValue(emptyList())
            return
        }

        suggestionsLoading.postValue(true)
        safeSubscribe(
            Observable.just(0)
            .switchMap { peakRepository.getSuggestions(keywords) }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe(
                { response ->
                    suggestionsLoading.postValue(false)
                  if (response.isSuccessful) {
                      suggestionsResults.postValue(response.body()!!.data.suggestions)
                  }
                },
                { suggestionsLoading.postValue(false) }
            )
        )
    }

    fun onSuggestionSelected(suggestion: Suggestion) {
        peakRepository.updateSearchSelection(suggestion)
    }

    fun getElevationText(elevation: Double): String {
        return unitManager.getSmallUnitFromFeet(elevation)
    }

    fun getLengthUnit(): LengthUnit {
        return unitManager.getLengthUnit()
    }
}