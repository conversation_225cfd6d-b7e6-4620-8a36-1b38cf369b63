package com.peakery.android.feature.history

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.model.Point
import com.peakery.android.inflate

class HistoryDetailAdapter(private val data: List<Point>): RecyclerView.Adapter<HistoryDetailAdapter.HistoryViewHolder>() {

    class HistoryViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        var title: TextView = itemView.findViewById(R.id.history_item_title)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder =
        HistoryViewHolder(parent.inflate(R.layout.item_history))

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: <PERSON><PERSON><PERSON>wHold<PERSON>, position: Int) {
        val point = data[position]
        holder.title.text = "lat: ${point.latitude} | lng: ${point.longitude}"
    }
}