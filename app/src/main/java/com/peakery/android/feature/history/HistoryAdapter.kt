package com.peakery.android.feature.history

import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.model.Track
import com.peakery.android.getFormattedTime
import com.peakery.android.inflate
import io.reactivex.subjects.PublishSubject
import java.text.DateFormat.getDateInstance
import java.text.DateFormat.getDateTimeInstance
import java.text.SimpleDateFormat
import java.util.*

class HistoryAdapter(private val data: List<HistoryData>): RecyclerView.Adapter<HistoryAdapter.HistoryViewHolder>() {

    var observer = PublishSubject.create<HistoryData>()
    var shareObserver = PublishSubject.create<HistoryData>()

    class HistoryViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        var title: TextView = itemView.findViewById(R.id.history_item_title)
        var share: ImageButton = itemView.findViewById(R.id.history_item_share)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): HistoryViewHolder =
        HistoryViewHolder(parent.inflate(R.layout.item_history))

    override fun getItemCount(): Int = data.size

    override fun onBindViewHolder(holder: HistoryViewHolder, position: Int) {
        val historyData = data[position]

        historyData.points.firstOrNull()?.let {
            val date = Date(it.time)
            val formatter = getDateTimeInstance().format(date)
            holder.title.text = formatter.format(date)
        }

        holder.title.setOnClickListener { observer.onNext(historyData) }
        holder.share.setOnClickListener { shareObserver.onNext(historyData) }
    }
}