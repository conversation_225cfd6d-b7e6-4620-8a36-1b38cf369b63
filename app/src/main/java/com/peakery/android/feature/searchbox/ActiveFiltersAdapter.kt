package com.peakery.android.feature.searchbox

import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.feature.filters.Filter
import com.peakery.android.inflate
import com.peakery.android.toYears
import java.text.DecimalFormat
import kotlin.math.ln
import kotlin.math.pow


class ActiveFiltersAdapter(val filters: List<Filter>, private val parentWidth: Int, private val model: SearchBoxViewModel):
    RecyclerView.Adapter<ActiveFiltersAdapter.ModifiedFilterViewHolder>() {

    private val ROW_COUNT = 4

    class ModifiedFilterViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val label: TextView = itemView.findViewById(R.id.modified_filter_text)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ModifiedFilterViewHolder =
        ModifiedFilterViewHolder(parent.inflate(R.layout.item_active_filter))

    override fun getItemCount(): Int {
        return if (filters.size > ROW_COUNT) {
            ROW_COUNT // Max filters
        } else {
            filters.size
        }
    }

    override fun onBindViewHolder(holder: ModifiedFilterViewHolder, position: Int) {
        holder.label.text = getTextForFilter(filters[position])
        holder.label.layoutParams.width = getViewWidth()

        if (position == (ROW_COUNT-1) && filters.size > ROW_COUNT) {
            holder.label.text = "More: ${getMoreCount()}"
        }
    }

    private fun getTextForFilter(filter: Filter): String {
        return when (filter.id) {
            "distance" -> getDistanceText(filter)
            "vertical_gain" -> getVerticalGainText(filter)
            "popularity" -> getPopularityText(filter)
            "last_climbed" -> getLastClimbedText(filter)
            "elevation" -> getElevationText(filter)
            "prominence" -> getProminenceText(filter)
            "difficulty" ->  getDifficultyText(filter)
            else -> filter.title!!
        }
    }

    private fun getLastClimbedText(filter: Filter): String {
        return when (val days = filter.values!![filter.selectedPosition!!]) {
            1 -> "Last day"
            in 2..90 -> "Last $days days"
            365 -> "Last year"
            in 91..99999 -> "Last ${days.toYears()} years"
            else -> "Any time"
        }
    }

    private fun getDifficultyText(filter: Filter): String {
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "Class ${filter.getMinValue()}+"
        } else {
            "Class ${filter.getMinValue()} to ${filter.getMaxValue()}"
        }
    }

    private fun getDistanceText(filter: Filter): String {
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "${filter.selectedRange!!.min}+ ${getKmOrMiles()}"
        } else {
            "${filter.selectedRange!!.min} to ${filter.selectedRange.max}${getPlusOrEmpty(filter)} ${getKmOrMiles()}"
        }
    }

    private fun getPopularityText(filter: Filter): String {
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "${filter.getMinValue()}+ climbs"
        } else {
            "${filter.getMinValue()}-${filter.getMaxValue()}${getPlusOrEmpty(filter)} climbs"
        }
    }

    private fun getVerticalGainText(filter: Filter): String {
        val minValue = filter.getMinValue()
        val maxValue = filter.getMaxValue()
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "${shortNumberFormat(minValue)}+ ${getSmallLengthUnit()} gain"
        } else {
            "${shortNumberFormat(minValue)}-${shortNumberFormat(maxValue)}${getPlusOrEmpty(filter)} ${getSmallLengthUnit()} gain"
        }
    }

    private fun getElevationText(filter: Filter): String {
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "${shortNumberFormat(filter.getMinValue())}+ ${getSmallLengthUnit()} elev"
        } else {
            "${shortNumberFormat(filter.getMinValue())}-${shortNumberFormat(filter.getMaxValue())} ${getSmallLengthUnit()} elev"
        }
    }

    private fun getProminenceText(filter: Filter): String {
        return if (!filter.isMinDefault() && filter.isMaxDefault()) {
            "${shortNumberFormat(filter.getMinValue())}+ ${getSmallLengthUnit()} prom"
        } else {
            "${shortNumberFormat(filter.getMinValue())}-${shortNumberFormat(filter.getMaxValue())} ${getSmallLengthUnit()} prom"
        }
    }

    private fun getKmOrMiles(): String {
        return when (model.getLengthUnit()) {
            LengthUnit.IMPERIAL -> "mi"
            LengthUnit.METRIC -> "km"
        }
    }


    private fun getSmallLengthUnit(): String {
        return when (model.getLengthUnit()) {
            LengthUnit.IMPERIAL -> "ft"
            LengthUnit.METRIC -> "m"
        }
    }

    private fun getMoreCount(): Int {
        return if (filters.size > ROW_COUNT) {
            filters.size - ROW_COUNT
        } else {
            0
        }
    }

    private fun getViewWidth() = parentWidth / itemCount

    private fun getPlusOrEmpty(filter: Filter): String {
        val values = filter.values
        val maxValue = filter.selectedRange!!.max
        return if (values != null && values.last() == maxValue) {
            "+"
        } else if (maxValue == filter.defaultRange!!.max) {
            "+"
        } else {
            ""
        }
    }

    private fun shortNumberFormat(count: Int): String {
        if (count < 1000) return "" + count
        val exp = (ln(count.toDouble()) / ln(1000.0)).toInt()
        val format = DecimalFormat("0.#")
        val value = format.format(count / 1000.0.pow(exp.toDouble()))
        return String.format("%s%c", value, "KMBTPE"[exp - 1])
    }
}

