package com.peakery.android.feature.settings

import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.state.UserPreferences

class DeveloperSettingsViewModel(private val userPreferences: UserPreferences): PeakeryViewModel() {

    fun toggleOfflinePeaks(enabled: <PERSON><PERSON><PERSON>) {
        userPreferences.enableOfflinePeaks(enabled)
    }

    fun toggleDebugLocation(enabled: Boolean) {
        userPreferences.enableDebugLocation(enabled)
    }

    fun updateGpsInterval(interval: Long) {
        userPreferences.setGpsInterval(interval)
    }

    fun updateFastGpsInterval(interval: Long) {
        userPreferences.setFastGpsInterval(interval)
    }

    fun updateTrackingOffInterval(interval: Long) {
        userPreferences.setTrackingOffInterval(interval)
    }

    fun isOfflinePeaksEnabled() = userPreferences.isOfflinePeaksEnabled()

    fun isDebugLocationsEnabled(): Boolean = userPreferences.isDebugLocationsEnabled()

    fun getGpsInterval() = userPreferences.getGpsInterval()

    fun getFastGpsInterval() = userPreferences.getFastGpsInterval()

    fun getTrackingOffGpsInterval() = userPreferences.getTrackingOffGpsInterval()
}