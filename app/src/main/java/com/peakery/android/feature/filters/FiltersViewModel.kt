package com.peakery.android.feature.filters

import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences

class FiltersViewModel(private val peakRepository: PeakRepository,
                       private val lenghtUnitManager: UserPreferences): PeakeryViewModel() {

    val filters = MutableLiveData<List<Filter>>()
    private lateinit var filtersData: MutableList<Filter>

    init {
        safeSubscribe(peakRepository.filtersObservable.subscribe { filters ->
            filtersData = filters.toMutableList()
            this.filters.postValue(filtersData)
        })
    }

//    fun updateSortFilter(filter: Filter, valuePosition: Int) {
//        val updatedValue = filter.imperialValues!![valuePosition]
//        if (filter.value != updatedValue) {
//            val newFilter = Filter(filter.id,
//                filter.type,
//                updatedValue,
//                filter.imperialValues,
//                filter.title,
//                filter.subtitle,
//                filter.defaultRange,
//                filter.selectedRange)
//            updateFilter(newFilter)
//        }
//    }

    fun updateRangeFilter(filter: Filter, newRange: Range) {
        if (filter.selectedRange != newRange) {
            val newFilter = Filter(filter.id,
                filter.type,
                filter.value,
                filter.values,
                filter.title,
                filter.subtitle,
                filter.defaultRange,
                newRange)
            updateFilter(newFilter)
        }
    }

    fun updateSelectionFilter(filter: Filter, newPosition: Int) {
        if (filter.selectedPosition != newPosition) {
            val newFilter = Filter(filter.id,
                filter.type,
                filter.value,
                filter.values,
                filter.title,
                filter.subtitle,
                filter.defaultRange,
                filter.selectedRange,
                newPosition)
            updateFilter(newFilter)
        }

    }

    fun getLengthUnit(): LengthUnit {
        return lenghtUnitManager.getLengthUnit()
    }

    private fun updateFilter(filter: Filter) {
        filtersData[filtersData.indexOfFirst { it.id == filter.id }] = filter
    }

    override fun onCleared() {
        peakRepository.updateFilters(filtersData)
        super.onCleared()
    }

    fun resetFilters() {
        peakRepository.resetFilters()
    }
}