package com.peakery.android.feature.layers

import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel

class LayersViewModel(private val layerRepository: LayerRepository): PeakeryViewModel() {

    val allLayers = MutableLiveData<List<Layer>>()
    val layerSelected = MutableLiveData<Layer>()

    init {
        allLayers.postValue(layerRepository.allLayers)
        safeSubscribe(layerRepository.layerObservable.subscribe {
            layerSelected.postValue(it)
        })
    }

    fun onLayerSelected(layer: Layer) {
        layerRepository.onLayerSelected(layer)
    }
}