package com.peakery.android.feature.main

import androidx.lifecycle.MutableLiveData

class UiCoordinator {

    val bottomContentHeight = MutableLiveData<Float>()
    val filtersPanel = MutableLiveData<Boolean>()
    val activeFilters = MutableLiveData<Boolean>()

    fun updateBottomContentHeight(y: Float) {
        bottomContentHeight.postValue(y)
    }

    fun onFilterPanelVisibilityChange(visible: Boolean) {
        filtersPanel.postValue(visible)
    }

    fun onActiveFiltersVisibilityChange(visible: Boolean) {
        activeFilters.postValue(visible)
    }

    fun isFilterPanelOpen(): Boolean {
        return filtersPanel.value != null && filtersPanel.value == true
    }
}