package com.peakery.android.feature.peaks

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.peakery.android.R
import com.peakery.android.core.model.Peak
import com.peakery.android.inflate
import io.reactivex.subjects.PublishSubject

class PeakListAdapter(private val context: Context, val model: PeakListViewModel): RecyclerView.Adapter<PeakListAdapter.PeakViewHolder>() {

    var peaks = emptyList<Peak>()

    var observer = PublishSubject.create<Peak>()

    class PeakViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val layout: ViewGroup = itemView.findViewById(R.id.map_peak_layout)
        val title: TextView = itemView.findViewById(R.id.map_peak_title)
        val summary: TextView = itemView.findViewById(R.id.map_peak_summary)
        val yourSummits: TextView = itemView.findViewById(R.id.map_peak_label_summits)
        val labelChallenge: TextView = itemView.findViewById(R.id.map_peak_label_challenge)
        val labelClassic: TextView = itemView.findViewById(R.id.map_peak_label_classic)
        val image: ImageView = itemView.findViewById(R.id.map_peak_image)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PeakViewHolder = PeakViewHolder(parent.inflate(R.layout.item_peak))

    override fun getItemCount(): Int = peaks.size

    override fun onBindViewHolder(holder: PeakViewHolder, position: Int) {
        val peak = peaks[position]

        holder.title.text = peak.name

        if (peak.challenge_count > 0) {
            holder.labelChallenge.visibility = View.VISIBLE
            holder.labelChallenge.text = context.resources.getQuantityString(R.plurals.challenges, peak.challenge_count, peak.challenge_count)
        } else {
            holder.labelChallenge.visibility = View.GONE
        }

        if (peak.your_summits > 0) {
            holder.yourSummits.visibility = View.VISIBLE
            holder.yourSummits.text = "${peak.your_summits}x"
        } else {
            holder.yourSummits.visibility = View.GONE
        }
        if (peak.is_classic) {
            holder.labelClassic.visibility = View.VISIBLE
        } else {
            holder.labelClassic.visibility = View.GONE
        }

        val summits = context.resources.getQuantityString(R.plurals.summits, peak.summit_count, peak.summit_count)
        holder.summary.text = "${model.getElevationText(peak.elevation)} • $summits"

        Glide.with(context)
            .load(peak.thumbnail_url)
            .transition(DrawableTransitionOptions.withCrossFade())
            .centerCrop()
            .into(holder.image)

        holder.layout.setOnClickListener { observer.onNext(peak) }
    }

    fun update(peaks: List<Peak>) {
        this.peaks = peaks
        notifyDataSetChanged()
    }

    fun clear() {
        this.peaks = emptyList()
        notifyDataSetChanged()
    }
}
