package com.peakery.android.feature.map

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment

/**
 * Quick hack for the FragmentManager transition animation to work upon removal of the real
 * MapPeakFragment. This is a dummy Fragment of the same height
 */
class EmptyMapPeakFragment: PeakeryFragment(R.layout.fragment_map_peak_empty)