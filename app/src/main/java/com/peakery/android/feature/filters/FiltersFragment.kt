package com.peakery.android.feature.filters

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.ui.ExpandedBottomSheetFragment
import com.peakery.android.core.ui.SeparatorDecoration
import com.peakery.android.feature.main.UiCoordinator
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

open class FiltersFragment: ExpandedBottomSheetFragment() {

    val model: FiltersViewModel by viewModel()
    private val uiCoordinator: UiCoordinator by inject()

    private lateinit var filters_list: RecyclerView
    private lateinit var filters_reset: View

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? =
            inflater.inflate(R.layout.fragment_filters, container, false)

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        filters_list = requireView().findViewById(R.id.filters_list)
        filters_reset = requireView().findViewById(R.id.filters_reset)

        filters_list.addItemDecoration(SeparatorDecoration(requireContext(), Color.LTGRAY, 1f))
        filters_list.setScrollingTouchSlop(RecyclerView.TOUCH_SLOP_PAGING)
        model.filters.observe(viewLifecycleOwner, Observer { filters ->
            filters_list.adapter = FiltersAdapter(filters, model)
        })

        filters_reset.setOnClickListener {
            model.resetFilters()
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        uiCoordinator.onFilterPanelVisibilityChange(true)
    }

    override fun onDetach() {
        uiCoordinator.onFilterPanelVisibilityChange(false)
        super.onDetach()
    }

}
