package com.peakery.android.feature.auth

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity



class AuthActivity: PeakeryActivity() {

    companion object { fun newIntent(context: Context) = Intent(context, AuthActivity::class.java) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_auth)

        // Animate in
        overridePendingTransition(R.anim.bottom_in, R.anim.stay)
    }

    override fun finish() {
        super.finish()
        // Animate out
        overridePendingTransition(R.anim.stay, R.anim.bottom_out)
    }

    override fun onBackPressed() {
        super.onBackPressed()
        val fragment = supportFragmentManager.findFragmentById(R.id.fragment_auth) as AuthFragment
        fragment.onBackPressed()
    }
}