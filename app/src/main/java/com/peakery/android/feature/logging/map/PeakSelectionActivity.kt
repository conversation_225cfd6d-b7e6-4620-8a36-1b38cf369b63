package com.peakery.android.feature.logging.map

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.location.Location
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.transition.TransitionManager
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatImageButton
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.google.android.gms.maps.model.LatLng
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.JsonParser
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.mapbox.android.gestures.MoveGestureDetector
import com.mapbox.geojson.Point
import com.mapbox.mapboxsdk.Mapbox
import com.mapbox.mapboxsdk.camera.CameraUpdateFactory
import com.mapbox.mapboxsdk.constants.MapboxConstants
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.mapbox.mapboxsdk.maps.MapView
import com.mapbox.mapboxsdk.maps.MapboxMap
import com.mapbox.mapboxsdk.maps.Style
import com.mapbox.mapboxsdk.plugins.annotation.OnSymbolClickListener
import com.mapbox.mapboxsdk.plugins.annotation.Symbol
import com.mapbox.mapboxsdk.plugins.annotation.SymbolManager
import com.mapbox.mapboxsdk.plugins.annotation.SymbolOptions
import com.mapbox.mapboxsdk.style.layers.RasterLayer
import com.mapbox.mapboxsdk.style.sources.RasterSource
import com.mapbox.mapboxsdk.style.sources.TileSet
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.latLng
import com.peakery.android.core.ui.MapButtons
import com.peakery.android.feature.layers.Layer
import com.peakery.android.feature.layers.LayersFragment
import com.peakery.android.feature.map.EmptyMapPeakFragment
import com.peakery.android.snack
import com.peakery.android.toLatLng
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.*
import java.util.concurrent.TimeUnit

private const val ICON_PEAK_BLUE = "peak_blue"
private const val ICON_PEAK_GREEN = "peak_green"
private const val ICON_PEAK_ORANGE = "peak_orange"
private const val ICON_PEAK_RED = "peak_red"
private const val ICON_PEAK_YELLOW = "peak_yellow"

class PeakSelectionActivity: PeakeryActivity() {

    companion object { fun start(activity: Activity?) = activity?.startActivity(Intent(activity, PeakSelectionActivity::class.java)) }

    private val model: PeakSelectionViewModel by viewModel()

    private lateinit var map: MapboxMap
    private lateinit var style: Style

    private var mapPeakFragment: Fragment? = null
    private val mapMoveHandler = Handler(Looper.getMainLooper())

    private lateinit var symbolManager: SymbolManager
    private lateinit var highestSymbolManager: SymbolManager
    private lateinit var selectedSymbolManager: SymbolManager
    private val symbolDeque: Deque<ArrayList<Symbol>> = LinkedList()
    private var lastSelectedSymbol: Symbol? = null

    private lateinit var lastBounds: LatLngBounds

    private lateinit var peak_selection_map: MapView
    private lateinit var peak_selection_search_loader: ProgressBar
    private lateinit var peak_selection_map_buttons: MapButtons
    private lateinit var peak_selection_button_cancel: Button
    private lateinit var peak_selection_button_save: Button
    private lateinit var peak_selection_search_input: EditText
    private lateinit var peak_selection_search_clear: AppCompatImageButton
    private lateinit var peak_selection_controls: ViewGroup
    private lateinit var peak_selection_search_go: Button

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Mapbox.getInstance(this, getString(R.string.access_token))
        setContentView(R.layout.activity_peak_selection)
        initView()
        peak_selection_map.onCreate(savedInstanceState)

        model.loading.observe(this, Observer { loading ->
            peak_selection_search_loader.visibility = if (loading) View.VISIBLE else View.GONE
        })

        peak_selection_map.getMapAsync {
            this.map = it

            map.uiSettings.isCompassEnabled = false
            map.uiSettings.isRotateGesturesEnabled = false
            map.uiSettings.isTiltGesturesEnabled = false

            map.setStyle(Style.OUTDOORS) { style -> initStyle(style) }

            map.addOnCameraIdleListener {
//                toggleSymbolOverlap()
                updateMapBounds()
            }
        }

        initUiObservers()
    }

    private fun initView() {
        peak_selection_map = findViewById(R.id.peak_selection_map)
        peak_selection_search_loader = findViewById(R.id.peak_selection_search_loader)
        peak_selection_map_buttons = findViewById(R.id.peak_selection_map_buttons)
        peak_selection_button_cancel = findViewById(R.id.peak_selection_button_cancel)
        peak_selection_button_save = findViewById(R.id.peak_selection_button_save)
        peak_selection_search_input = findViewById(R.id.peak_selection_search_input)
        peak_selection_search_clear = findViewById(R.id.peak_selection_search_clear)
        peak_selection_controls = findViewById(R.id.peak_selection_controls)
        peak_selection_search_go = findViewById(R.id.peak_selection_search_go)
    }

    @SuppressLint("CheckResult")
    private fun initUiObservers() {
        peak_selection_map_buttons.findViewById<AppCompatImageButton>(R.id.map_button_location).setOnClickListener {
            centerUserLocation(model.lastKnownLocation())
        }

        RxView.clicks(peak_selection_map_buttons.findViewById(R.id.map_button_layers))
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe {
                val layersFragment = LayersFragment()
                layersFragment.show(supportFragmentManager, layersFragment.tag)
            }

        peak_selection_button_cancel.setOnClickListener { finish() }
        peak_selection_button_save.setOnClickListener { selectPeak() }

        RxView.keys(peak_selection_search_input)
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .filter { key -> (key.keyCode == KeyEvent.KEYCODE_SEARCH || key.keyCode == KeyEvent.KEYCODE_ENTER) }
            .subscribe {
                hideKeyboard()
                model.searchPlace(peak_selection_search_input.text.toString())
            }

        RxView.clicks(peak_selection_search_go)
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe {
                hideKeyboard()
                model.searchPlace(peak_selection_search_input.text.toString())
            }

        RxTextView.textChanges(peak_selection_search_input)
            .debounce(300, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                peak_selection_search_clear?.visibility = if (it.isNullOrEmpty()) {
                    View.GONE
                } else {
                    View.VISIBLE
                }
            }

        peak_selection_search_clear.setOnClickListener {
            peak_selection_search_input.text.clear()
        }

    }

    private fun centerUserLocation(location: Location?) {
        unselectPeak()
        if (location != null) {
            map.animateCamera(CameraUpdateFactory.newLatLngZoom(location.toLatLng(), 12.0))
        }
    }

    private fun unselectPeak() {
        model.unselectPeak()

        TransitionManager.beginDelayedTransition(peak_selection_controls)
        peak_selection_button_save.visibility = View.GONE

        val transaction = supportFragmentManager.beginTransaction()
        transaction.setCustomAnimations(R.anim.slide_in_up, R.anim.slide_in_down, R.anim.slide_out_down, R.anim.slide_out_up)
        transaction.replace(R.id.peak_selection_fragment_container, EmptyMapPeakFragment()).commit()
        mapPeakFragment = null
        resetLastSelectedSymbol()
        peak_selection_map_buttons.visibility = View.VISIBLE
    }

    private fun resetLastSelectedSymbol() {
        if (::selectedSymbolManager.isInitialized) {
            selectedSymbolManager.deleteAll()
        }
        lastSelectedSymbol = null
    }

    private fun initStyle(style: Style) {
        updateStyle(style)

        // mapbox seems to fail to change layer right away in activities, causing
        // callback failures later on. adding a delay fixes that
        Handler().postDelayed({ initMapObservers() }, 200)

        map.gesturesManager.moveGestureDetector.moveThreshold = 3f
        map.addOnMoveListener(object: MapboxMap.OnMoveListener {
            override fun onMove(detector: MoveGestureDetector) {}
            override fun onMoveEnd(detector: MoveGestureDetector) {}
            override fun onMoveBegin(detector: MoveGestureDetector) { unselectPeak() }
        })
//        map.addOnMapClickListener {
//            if (mapPeakFragment != null) {
//                unselectPeak()
//                true
//            }
//            hideKeyboard()
//            false
//        }
    }

    private fun initMapObservers() {
        model.layer.observe(this, Observer {
            clearMapLayers()
            setLayer(it)
        })
        model.peaks.observe(this, Observer { peaks ->
            try {
                updateMapSymbols(peaks)
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }
        })
        model.search.observe(this, Observer { latLng ->
            map.animateCamera {
                CameraUpdateFactory.newLatLngZoom(latLng, 12.0)
                    .getCameraPosition(map)
            }
        })
        model.peakSelection.observe(this, Observer {
            androidx.transition.TransitionManager.beginDelayedTransition(peak_selection_controls)
//            TransitionManager.beginDelayedTransition(peak_selection_controls)
            peak_selection_button_save.visibility = if (it.isPresent) View.VISIBLE else View.GONE
        })
        model.selectedPeaks.observe(this, Observer { peaks ->
            when {
                peaks.size == 1 -> {
                    map.moveCamera {
                        CameraUpdateFactory.newLatLngZoom(peaks.first().latLng(), 15.0).getCameraPosition(map)
                    }
                }
                peaks.size > 1 -> {
                    val bounds = LatLngBounds.Builder()
                    for (peak in peaks) {
                        bounds.include(peak.latLng())
                    }
                    map.moveCamera {
                        CameraUpdateFactory.newLatLngBounds(bounds.build(), 200)
                            .getCameraPosition(map)
                    }
                    map.moveCamera {
                        CameraUpdateFactory.zoomBy(-2.0).getCameraPosition(map)
                    }
                }
            }

        })
    }

    private fun clearMapLayers() {
        if (style.isFullyLoaded.not()) return
        style.sources.forEach { source ->
            model.allLayers().forEach {
                if (source.id == it.sourceId) { style.removeSource(source) }
            }
        }
        style.layers.forEach { layer ->
            model.allLayers().forEach {
                if (layer.id == it.layerId) { style.removeLayer(layer) }
            }
        }
    }

    private fun setLayer(layer: Layer) {
        when (layer.sourceId) {
            "terrain" -> map.setStyle(Style.Builder().fromUrl(model.getTerrainUrl())) { updateStyle(it) }
            "satellite" -> map.setStyle(Style.Builder().fromUrl(model.getSatelliteUrl())) { updateStyle(it) }
            "satellite-topo" -> map.setStyle(Style.Builder().fromUrl(model.getSatelliteTopoUrl())) { updateStyle(it) }
            else -> {
                map.setStyle(Style.OUTDOORS) {
                    updateStyle(it)
                    val customTiles = RasterSource(
                        layer.sourceId,
                        TileSet("raster", *layer.tileUrl),
                        256
                    )
                    style.addSource(customTiles)
                    style.addLayerBelow(RasterLayer(layer.layerId, layer.sourceId), MapboxConstants.LAYER_ID_ANNOTATIONS)
                }
            }
        }
    }

    private fun updateMapBounds() {
        mapMoveHandler.removeCallbacksAndMessages(boundsRunnable)
        lastBounds = map.projection.getVisibleRegion(false).latLngBounds
        mapMoveHandler.postDelayed(boundsRunnable, 500)
    }

    private val boundsRunnable = Runnable {
        model.updateMapBounds(lastBounds)
    }

    private fun updateStyle(style: Style) {
        symbolDeque.clear()
        this.style = style
        if (::symbolManager.isInitialized) symbolManager.annotations.clear()
        if (::highestSymbolManager.isInitialized) highestSymbolManager.annotations.clear()
        if (::selectedSymbolManager.isInitialized) selectedSymbolManager.annotations.clear()

        symbolManager = SymbolManager(peak_selection_map, map, style)
        highestSymbolManager = SymbolManager(peak_selection_map, map, style)
        selectedSymbolManager = SymbolManager(peak_selection_map, map, style)

        symbolManager.iconPadding = 5f
        highestSymbolManager.iconPadding = 5f
        selectedSymbolManager.iconPadding = 5f

        toggleSymbolOverlap()

        style.addImage(ICON_PEAK_BLUE, BitmapFactory.decodeResource(resources, R.drawable.peak_blue))
        style.addImage(ICON_PEAK_GREEN, BitmapFactory.decodeResource(resources, R.drawable.peak_green))
        style.addImage(ICON_PEAK_ORANGE, BitmapFactory.decodeResource(resources, R.drawable.peak_orange))
        style.addImage(ICON_PEAK_RED, BitmapFactory.decodeResource(resources, R.drawable.peak_red))
        style.addImage(ICON_PEAK_YELLOW, BitmapFactory.decodeResource(resources, R.drawable.peak_orange))

        val symbolClickListener = OnSymbolClickListener {
            model.onTapPeakOnMap(it.getPeakId())
            selectSymbol(it)
            true
        }

        symbolManager.addClickListener(symbolClickListener)
        highestSymbolManager.addClickListener(symbolClickListener)

        model.peaks.value?.let {
            updateMapSymbols(it)
        }
    }

    private fun updateMapSymbols(peaks: List<Peak>) {
        if (symbolDeque.size >= 2) {
            symbolManager.delete(symbolDeque.removeLast())
        }
        val newSymbols = arrayListOf<Symbol>()

        // highest
        highestSymbolManager.deleteAll()
        val tallestPeak = peaks.filter { it.latLng() != lastSelectedSymbol?.latLng }.maxBy { it.elevation }

        // all peaks
        peaks.forEach { peak ->
            if (peak == tallestPeak) return@forEach

            val symbolOptions = SymbolOptions()
                .withPeakId(peak.id)
                .withLatLng(peak.latLng())

            if (peak.your_summits > 0) {
                symbolOptions.withIconImage(ICON_PEAK_GREEN)
            } else {
                symbolOptions.withIconImage(ICON_PEAK_BLUE)
            }

            // Clear duplicate symbol from preview deque
            if (symbolDeque.size > 0) {
                val duplicateSymbol = symbolDeque.first?.find { it.latLng == peak.latLng() }
                if (duplicateSymbol != null) {
                    symbolManager.delete(duplicateSymbol)
                    symbolDeque.first.remove(duplicateSymbol)
                }
            }
            newSymbols.add(symbolManager.create(symbolOptions))
        }

        if (tallestPeak != null) {
            val symbolOptions = SymbolOptions()
                .withPeakId(tallestPeak.id)
                .withLatLng(tallestPeak.latLng())
                .withIconImage(ICON_PEAK_RED)

            highestSymbolManager.create(symbolOptions)
        }

        if (lastSelectedSymbol != null) {
            selectSymbol(lastSelectedSymbol!!)
        }
        symbolDeque.addFirst(newSymbols)
    }

    private fun toggleSymbolOverlap() {
        val allowOverlap = map.cameraPosition.zoom >= 12
        symbolManager.iconAllowOverlap = allowOverlap
        symbolManager.textAllowOverlap = allowOverlap
    }

    private fun selectSymbol(symbol: Symbol) {
        if (mapPeakFragment == null) {
            mapPeakFragment = PeakSelectionMapPeakFragment()
            safeSubscribe((mapPeakFragment as PeakSelectionMapPeakFragment).peakSelectedObserver.subscribeBy(
                onNext = { selectPeak() },
                onError = {it.localizedMessage}
            ))
            val transaction = supportFragmentManager.beginTransaction()
            transaction.setCustomAnimations(
                R.anim.slide_in_up,
                R.anim.slide_in_down,
                R.anim.slide_out_down,
                R.anim.slide_out_up
            )
            transaction.replace(R.id.peak_selection_fragment_container, mapPeakFragment!!).commit()
        }
        resetLastSelectedSymbol()

        val selectedSymbolOptions = SymbolOptions()
            .withLatLng(symbol.latLng)
            .withIconImage(ICON_PEAK_ORANGE)
        val selectedSymbol = selectedSymbolManager.create(selectedSymbolOptions)

        lastSelectedSymbol = selectedSymbol
        peak_selection_map_buttons.visibility = View.GONE
    }

    private fun selectPeak() {
        model.saveSelectedPeak()
        snack(R.string.peak_saved)
        finish()
    }

    override fun onPause() {
        super.onPause()
        peak_selection_map.onPause()
    }

    override fun onResume() {
        super.onResume()
        peak_selection_map.onResume()
    }

    override fun onStart() {
        super.onStart()
        peak_selection_map.onStart()
    }

    override fun onStop() {
        super.onStop()
        peak_selection_map.onStop()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        peak_selection_map.onSaveInstanceState(outState)
    }

    override fun onLowMemory() {
        super.onLowMemory()
        peak_selection_map.onLowMemory()
    }

    override fun onDestroy() {
        super.onDestroy()
        peak_selection_map?.onDestroy()
    }

    private val jsonParser = JsonParser()
    private fun SymbolOptions.withPeakId(id: String): SymbolOptions {
        return this.withData(jsonParser.parse(id))
    }

    private fun hideKeyboard() {
        val view = findViewById<View>(android.R.id.content)
        if (view != null) {
            val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }

    private fun Symbol.getPeakId(): String {
        return this.data!!.asString
    }
}

private fun LatLng.toMapBoxLatLng(): com.mapbox.mapboxsdk.geometry.LatLng = com.mapbox.mapboxsdk.geometry.LatLng(latitude, longitude)

fun Point.toLatLng(): com.mapbox.mapboxsdk.geometry.LatLng = com.mapbox.mapboxsdk.geometry.LatLng(latitude(), longitude())

