package com.peakery.android.feature.searchbox

import android.R.attr.x
import android.R.attr.y
import android.annotation.SuppressLint
import android.content.Context.INPUT_METHOD_SERVICE
import android.graphics.drawable.Drawable
import android.graphics.drawable.RippleDrawable
import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ProgressBar
import androidx.core.content.ContextCompat
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.TransitionManager
import com.jakewharton.rxbinding2.view.RxView
import com.jakewharton.rxbinding2.widget.RxTextView
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.model.latLng
import com.peakery.android.core.ui.SeparatorDecoration
import com.peakery.android.feature.filters.FiltersFragment
import com.peakery.android.feature.main.UiCoordinator
import io.reactivex.android.schedulers.AndroidSchedulers
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.util.concurrent.TimeUnit


class SearchBoxFragment: PeakeryFragment(R.layout.fragment_search_box) {

    private val model: SearchBoxViewModel by viewModel()
    private val uiCoordinator: UiCoordinator by inject()

    private lateinit var lastSuggestion: Suggestion

    private lateinit var search_box_button_filter: ImageButton
    private lateinit var search_active_filters: RecyclerView
    private lateinit var search_box_clear: ImageButton
    private lateinit var search_box_input: EditText
    private lateinit var suggestions_list: RecyclerView
    private lateinit var suggestions_loader:ProgressBar
    private lateinit var search_box_loader: ProgressBar
    private lateinit var search_box_root: ViewGroup

    companion object { fun new() : SearchBoxFragment = SearchBoxFragment() }

    @SuppressLint("CheckResult")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        initView()

        RxView.clicks(search_box_button_filter)
            .throttleFirst(300, TimeUnit.MILLISECONDS)
            .subscribe { showFilters() }

        search_active_filters.setOnTouchListener { view, motionEvent ->
            if (motionEvent.action == MotionEvent.ACTION_UP) {
                val background: Drawable = view.background
                if (background is RippleDrawable) {
                    background.setHotspot(x.toFloat(), y.toFloat())
                    background.setVisible(true, true)
                }
                view.isPressed = true
                view.isPressed = false
                showFilters()
                true
            }
            false
        }

        search_box_clear.setOnClickListener {
            search_box_input.text.clear()
            search_box_input.clearFocus()
            search_box_clear.requestFocus()
            search_box_clear.visibility = View.GONE
            search_box_input.hint = getString(R.string.peak_or_location)
            hideKeyboard()
        }

        model.defaultFilters.observe(viewLifecycleOwner, Observer { defaultFilters ->
            if (defaultFilters) {
                search_box_button_filter.setColorFilter(ContextCompat.getColor(requireContext(), R.color.colorAccent), android.graphics.PorterDuff.Mode.SRC_IN)
            } else {
                search_box_button_filter.setColorFilter(ContextCompat.getColor(requireContext(), R.color.grey_hint), android.graphics.PorterDuff.Mode.SRC_IN)
            }
        })

        setupActiveFilters()

        RxTextView.textChanges(search_box_input)
            .debounce(300, TimeUnit.MILLISECONDS)
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                search_box_clear?.visibility = if (it.isNullOrEmpty()) {
                    View.GONE
                } else {
                    View.VISIBLE
                }

                if (it.length > 2 || it.isEmpty()) {
                    model.search(it.toString())
                }
            }

        suggestions_list.addItemDecoration(SeparatorDecoration(requireContext(), -0x222222, 1f))

        model.suggestionsLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            suggestions_list.visibility = View.GONE
            suggestions_loader.visibility = if (isLoading && search_box_input.text.length > 2) View.VISIBLE else View.GONE
        })
        model.suggestionsResults.observe(viewLifecycleOwner, Observer { suggestions ->
            if (search_box_input.text.length > 2) {
                suggestions_list.visibility = View.VISIBLE
                val adapter = SearchBoxAdapter(suggestions, requireContext(), model)
                safeSubscribe(adapter.observer.subscribe {
                    model.onSuggestionSelected(it)
                    hideKeyboard()
                    goBack()
                })
                suggestions_list.adapter = adapter
            } else {
                suggestions_list.visibility = View.GONE
            }
        })
        model.selectedSuggestion.observe(viewLifecycleOwner, Observer { selection ->
            lastSuggestion = selection
            search_box_input.clearFocus()
            search_box_input.hint = "Near ${lastSuggestion.name}"
        })
        model.peaksLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            search_box_loader.visibility = if (isLoading) View.VISIBLE else View.GONE
        })
        model.mapCenterChange.observe(viewLifecycleOwner, Observer {
            if (::lastSuggestion.isInitialized && it.distanceTo(lastSuggestion.latLng) > 10000) { // 10km
                search_box_input.hint = getString(R.string.peak_or_location)
            }
        })
    }

    private fun initView() {
        search_box_button_filter = requireView().findViewById(R.id.search_box_button_filter)
        search_active_filters = requireView().findViewById(R.id.search_active_filters)
        search_box_clear = requireView().findViewById(R.id.search_box_clear)
        search_box_input = requireView().findViewById(R.id.search_box_input)
        suggestions_list = requireView().findViewById(R.id.suggestions_list)
        suggestions_loader = requireView().findViewById(R.id.suggestions_loader)
        search_box_loader = requireView().findViewById(R.id.search_box_loader)
        search_box_root = requireView().findViewById(R.id.search_box_root)
    }

    private fun setupActiveFilters() {
        val divider = DividerItemDecoration(activity, DividerItemDecoration.HORIZONTAL)
        divider.setDrawable(resources.getDrawable(R.drawable.list_divider_horizontal))
        search_active_filters.addItemDecoration(divider)
        search_active_filters.layoutManager = object: LinearLayoutManager(context, HORIZONTAL, false) {
            override fun canScrollHorizontally(): Boolean = false
        }

        model.activeFilters.observe(viewLifecycleOwner, Observer { refreshActiveFilters() })
        uiCoordinator.filtersPanel.observe(viewLifecycleOwner, Observer { refreshActiveFilters() })
    }

    private fun refreshActiveFilters() {
        val activeFilters = model.activeFilters.value
        if (!activeFilters.isNullOrEmpty() && !uiCoordinator.isFilterPanelOpen()) {
            uiCoordinator.onActiveFiltersVisibilityChange(true)
            TransitionManager.beginDelayedTransition(search_box_root)
            search_active_filters.visibility = View.VISIBLE
            search_active_filters.post {
                val width = search_active_filters.width
                val sidePadding = search_active_filters.paddingLeft + search_active_filters.paddingRight
                val adapter = ActiveFiltersAdapter(activeFilters, width - sidePadding, model)
                search_active_filters.adapter = adapter
            }
        } else {
            uiCoordinator.onActiveFiltersVisibilityChange(false)
            search_active_filters.visibility = View.GONE
        }
    }

    private fun showFilters() {
        val filtersFragment = FiltersFragment()
        filtersFragment.show(parentFragmentManager, filtersFragment.tag)
    }

    fun canGoBack(): Boolean = ::suggestions_list.isInitialized && suggestions_list.childCount > 0

    fun goBack() {
        search_box_input.text.clear()
        search_box_input.clearFocus()
        suggestions_list.visibility = View.GONE
        suggestions_loader.visibility = View.GONE
        suggestions_list.adapter = null
    }

    private fun hideKeyboard() {
        val view = requireActivity().findViewById<View>(android.R.id.content)
        if (view != null) {
            val imm = requireActivity().getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(view.windowToken, 0)
        }
    }
}
