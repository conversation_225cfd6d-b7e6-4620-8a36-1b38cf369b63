package com.peakery.android.feature.webview

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.transition.TransitionManager
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.Button
import android.widget.ImageButton
import android.widget.ProgressBar
import androidx.appcompat.widget.AppCompatImageButton
import androidx.lifecycle.Observer
import com.peakery.android.*
import com.peakery.android.core.EXTRA_STATE
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.state.StateMachine
import com.peakery.android.feature.logging.LoggingActivity
import com.peakery.android.feature.settings.SettingsActivity
import io.reactivex.android.schedulers.AndroidSchedulers
import org.koin.androidx.viewmodel.ext.android.viewModel

class WebViewFragment: PeakeryFragment(R.layout.fragment_webview) {

    companion object {
        fun new(state: StateMachine.State): WebViewFragment =
            WebViewFragment().apply {
                arguments = Bundle().apply {
                    putParcelable(EXTRA_STATE, state)
                }
            }
    }

    private val model: WebViewViewModel by viewModel()
    private lateinit var bundle: Bundle

    private var postponeClearHistory = false

    private lateinit var webview: WebView
    private lateinit var webview_root: ViewGroup
    private lateinit var webview_button_settings: AppCompatImageButton
    private lateinit var webview_button_back: AppCompatImageButton
    private lateinit var webview_loader: ProgressBar

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        retainInstance = true
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        webview = requireView().findViewById(R.id.webview)
        webview_root = webview.parent as ViewGroup
        webview_button_settings = requireView().findViewById(R.id.webview_button_settings)
        webview_button_back = requireView().findViewById(R.id.webview_button_back)
        webview_loader = requireView().findViewById(R.id.webview_loader)

        val state = arguments?.getParcelable<StateMachine.State>(EXTRA_STATE)
        if (state != null) {
            model.setState(state)
        } else {
            model.setUrlFromActivityBundle(requireActivity())
        }

        setWebViewClient()
        setWebViewSettings()
        setJavascriptInterface()

        model.url.observe(viewLifecycleOwner, Observer {
            loadUrl(it)
        })

        model.clearHistory.observe(viewLifecycleOwner, Observer {
            postponeClearHistory = false
            webview.clearHistory()
            updateBackButtonVisibility(webview.url)
            updateSettingsButtonVisibility()
        })

        webview_button_settings.setOnClickListener {
            startActivity(SettingsActivity.newIntent(requireActivity()))
        }

        webview_button_back.setOnClickListener {
            goBack()
        }
        updateBackButtonVisibility()

        webview_button_back.setMargins(top = getStatusBarHeightPlus(R.dimen.margin_top_webview_back))
        webview_button_settings.setMargins(top = getStatusBarHeightPlus(R.dimen.margin_top_webview_back))
    }

    private fun goBack() {
        if (webview.canGoBack()) {
            webview.goBack()
        } else if (activity is WebViewActivity){
            requireActivity().onBackPressed()
        }
    }

    fun onBackPressed(): Boolean {
        if (webview.canGoBack()) {
            webview.goBack()
            return true
        }
        return false
    }

    private fun setJavascriptInterface() {
        val peakJsInterface = PeakJsInterface()
        webview.addJavascriptInterface(peakJsInterface, "Android")

        safeSubscribe(peakJsInterface.webMapSelected
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                model.savePeakCoordinates(it)
                if (activity is WebViewActivity) {
                    requireActivity().finish()
                }
            }
        )

        safeSubscribe(peakJsInterface.loggingSelected
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                LoggingActivity.startWithPeak(activity, it)
                if (activity is WebViewActivity) {
                    requireActivity().finish()
                }
            }
        )

        safeSubscribe(peakJsInterface.goBack
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe {
                goBack()
            }
        )
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun setWebViewSettings() {
        webview.setBackgroundColor(resources.getColor(R.color.colorPrimary))
        webview.settings.javaScriptEnabled = true
//        webview.settings.setAppCacheEnabled(true)
//        webview.settings.setAppCachePath(requireContext().cacheDir.path)
//        webview.settings.cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK

        val params = webview.layoutParams as ViewGroup.MarginLayoutParams
        params.topMargin = getStatusBarHeight()
        webview.layoutParams = params
    }

    private fun setWebViewClient() {
        val client = PeakeryWebViewClient(requireContext())
        client.loadingComplete.observe(viewLifecycleOwner, Observer { url ->
            if (postponeClearHistory) {
                postponeClearHistory = false
                webview.clearHistory()
            }
            webview_loader.visibility = View.GONE
            updateBackButtonVisibility(url)
            updateSettingsButtonVisibility()
        })
        client.loadingError.observe(viewLifecycleOwner, Observer {
            Log.e("WebViewFragment", "onError url: ${webview.url}")
            if (postponeClearHistory) {
                postponeClearHistory = false
                webview.clearHistory()
            }
            webview.loadUrl("file:///android_asset/webview_error.html")
            updateBackButtonVisibility()
        })
        client.loadingStart.observe(viewLifecycleOwner, Observer {
            webview_loader.visibility = View.VISIBLE
            updateSettingsButtonVisibility()
        })
        webview.webViewClient = client
    }

    private fun updateSettingsButtonVisibility() {
        if (model.shouldShowSettings() && webview.canGoBack().not()) {
            webview_button_settings.visibility = View.VISIBLE
        } else {
            webview_button_settings.visibility = View.INVISIBLE
        }
    }

    private fun loadUrl(url: String?) {
        if (url == null) return

        if (webview.url == url) {
            val anim = ObjectAnimator.ofInt(webview, "scrollY", webview.scrollY, 0)
            anim.duration = 300
            anim.start()
            return
        }

        postponeClearHistory = true
        webview.clearHistory()
        webview.loadUrl(url, PeakeryWebViewClient.getMobileHeaders())
    }

    private fun updateBackButtonVisibility(url: String? = null) {
        // special urls don't show the back button
        var isSpecialUrl = false
        if (url != null) {
            isSpecialUrl = (url.matches(Regex(".*/edit.*")))
                .or(url.matches(Regex(".*/photos.*")))
                .or(url.matches(Regex(".*/awards.*")))
//                .or(url.matches(Regex(".*/mobilebadges.*")))
                .or(url.matches(Regex(".*/log_climb.*")))
                .or(url.matches(Regex("file:///.*")))
        }

        try {
            TransitionManager.beginDelayedTransition(webview_root)
        } catch (ignore: Exception) { }

        webview_button_back.visibility = if ((webview.canGoBack() || activity is WebViewActivity) && !isSpecialUrl) View.VISIBLE else View.GONE
    }

    override fun onPause() {
        super.onPause()
        bundle = Bundle()
        webview.saveState(bundle)
    }
}
