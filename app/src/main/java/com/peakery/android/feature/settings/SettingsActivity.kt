package com.peakery.android.feature.settings

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity

class SettingsActivity: PeakeryActivity() {

    companion object { fun newIntent(context: Context) = Intent(context, SettingsActivity::class.java) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)
        supportActionBar?.title = getString(R.string.settings)
    }
}