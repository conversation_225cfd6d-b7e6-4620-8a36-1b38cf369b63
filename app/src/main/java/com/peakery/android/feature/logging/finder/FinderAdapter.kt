package com.peakery.android.feature.logging.finder

import android.content.Context
import android.icu.text.RelativeDateTimeFormatter
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.getRelativeTimeInDays
import com.peakery.android.inflate
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class FinderAdapter(context: Context): RecyclerView.Adapter<FinderViewHolder>() {

    val data = mutableListOf<File>()
    val observable = MutableLiveData<File>()

    private val res = context.resources
    private val dateFormat = RelativeDateTimeFormatter.getInstance()

    override fun getItemCount(): Int = data.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): FinderViewHolder =
        FinderViewHolder(parent.inflate(R.layout.item_finder_file))

    override fun onBindViewHolder(holder: FinderViewHolder, position: Int) {
        val file = data[position]

        holder.title.text = file.name

        val relativeTimeStr = dateFormat.format(
            file.lastModified().getRelativeTimeInDays().toDouble(),
            RelativeDateTimeFormatter.Direction.LAST,
            RelativeDateTimeFormatter.RelativeUnit.DAYS
        )
        holder.meta.text = "${file.sizeStrInKb()}, $relativeTimeStr"

        holder.layout.setOnClickListener {
            observable.postValue(file)
        }
    }

    fun add(file: File) {
        data.add(file)
        data.sortByDescending { it.lastModified() }
        notifyDataSetChanged()
    }
}

class FinderViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
    val layout: View = itemView.findViewById(R.id.item_finder_layout)
    val title: TextView = itemView.findViewById(R.id.item_finder_file_name)
    val meta: TextView = itemView.findViewById(R.id.item_finder_file_meta)
}

val File.size get() = if (!exists()) 0.0 else length().toDouble()
val File.sizeInKb get() = size / 1024
fun File.sizeStrInKb(decimals: Int = 0): String = "%.${decimals}f".format(sizeInKb) + " kB"
