package com.peakery.android.feature.tabs

import androidx.lifecycle.MutableLiveData
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.StateUpdate
import com.peakery.android.core.location.Tracker
import com.peakery.android.feature.main.UiCoordinator

class TabsViewModel(private val stateMachine: StateMachine,
                    private val tracker: Tracker,
                    private val uiCoordinator: UiCoordinator,
                    private val userRepository: UserRepository): PeakeryViewModel() {

    val state = MutableLiveData<StateUpdate>()

    init { stateMachine.state.subscribe { state.postValue(it) } }

    fun onMapSelected() {
        stateMachine.updateState(StateMachine.State.EXPLORE_MAP)
    }

    fun onListSelected() {
        stateMachine.updateState(StateMachine.State.EXPLORE_LIST)
    }

    fun onLatestSelected() {
        stateMachine.updateState(StateMachine.State.LATEST)
    }

    fun onProfileSelected() {
        stateMachine.updateState(StateMachine.State.PROFILE)
    }

    fun onRecordSelected() {
        tracker.resetTrackingState()
        stateMachine.updateState(StateMachine.State.TRACKING)
    }

    fun isAuthenticated() = userRepository.isAuthenticated()
}