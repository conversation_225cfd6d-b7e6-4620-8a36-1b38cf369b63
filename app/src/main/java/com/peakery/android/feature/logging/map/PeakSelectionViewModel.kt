package com.peakery.android.feature.logging.map

import android.annotation.SuppressLint
import android.content.Context
import android.location.Location
import androidx.lifecycle.MutableLiveData
import com.google.android.libraries.places.api.model.AutocompleteSessionToken
import com.mapbox.api.geocoding.v5.MapboxGeocoding
import com.mapbox.api.geocoding.v5.models.GeocodingResponse
import com.mapbox.mapboxsdk.geometry.LatLng
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.model.Peak
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.layers.Layer
import com.peakery.android.feature.layers.LayerRepository
import com.peakery.android.log
import io.reactivex.rxkotlin.subscribeBy
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.util.*

class PeakSelectionViewModel(private val peakRepository: PeakRepository,
                             private val tracker: Tracker,
                             private val userPreferences: UserPreferences,
                             private val peakSelectionRepository: PeakSelectionRepository,
                             private val layerRepository: LayerRepository,
                             private val context: Context
    ): PeakeryViewModel() {

    fun lastKnownLocation(): Location? = tracker.lastKnownLocation()

    val search = MutableLiveData<LatLng>()
    val peakSelection = MutableLiveData<Optional<Peak>>()
    val selectedPeaks = MutableLiveData<List<Peak>>()
    val peaks = MutableLiveData<List<Peak>>()
    val loading = MutableLiveData<Boolean>()
    val layer = MutableLiveData<Layer>()
    var geocoding: MapboxGeocoding? = null

    private val loaders = mutableListOf<String>()
    private var lastLatLngBounds: LatLngBounds? = null

    init {
        safeSubscribe(peakSelectionRepository.selectedPeaks.subscribe { selectedPeaks.postValue(it) })
        safeSubscribe(layerRepository.layerObservable.subscribe { layer.postValue(it) })
    }

    fun updateMapBounds(latLngBounds: LatLngBounds) {
        val tag = latLngBounds.toString()
        updateLoading(true, tag)
        lastLatLngBounds = latLngBounds
        safeSubscribe(
            peakRepository.getPeakSelectionForBounds(latLngBounds)
                .subscribeBy(
                    onNext = {
                        updateLoading(false, tag)
                        if (it.isSuccessful) {
                            val results = it.body()!!
                            peaks.postValue(results.data.peaks)
                        } else {
                            // Handle error
                        }
                    },
                    onError = {
                        updateLoading(false, tag)
                        it.log()
                    }
                )
        )
    }

    @SuppressLint("CheckResult")
    fun onTapPeakOnMap(peakId: String) {
        peakRepository.getPeak(peakId).subscribe {
            peakSelection.postValue(Optional.of(it))
        }
    }

    fun unselectPeak() {
        peakSelection.postValue(Optional.empty())
    }


    fun getElevationText(elevation: Double): String {
        return userPreferences.getSmallUnitFromFeet(elevation)
    }

    fun searchPlace(q: String) {
        geocoding?.cancelCall()

        if (q.length >= 2) {
            updateLoading(true, q)

            MapboxGeocoding.builder()
                .accessToken(context.getString(R.string.access_token))
                .query(q)
                .build()
                .enqueueCall(object: Callback<GeocodingResponse> {
                    override fun onResponse(
                        call: Call<GeocodingResponse>,
                        response: Response<GeocodingResponse>
                    ) {
                        updateLoading(false, q)
                        if (response.isSuccessful && !response.body()?.features().isNullOrEmpty()) {
                            val point = response.body()!!.features().first().center()!!
                            search.postValue(point.toLatLng())
                        }
                    }

                    override fun onFailure(call: Call<GeocodingResponse>, t: Throwable) {
                        updateLoading(false, q)
                    }
                })
        }
    }

    private fun updateLoading(loading: Boolean, element: String) {
        if (loading && !loaders.contains(element)) {
            loaders.add(element)
        } else if (!loading && loaders.contains(element)) {
            loaders.remove(element)
        }
        this.loading.postValue(loaders.isNotEmpty())
    }

    fun saveSelectedPeak() {
        if (peakSelection.value?.isPresent == true) {
            val peak = peakSelection.value!!.get()
            selectPeak(peak)
        }
    }

    fun allLayers() = layerRepository.allLayers

    fun getTerrainUrl(): String {
        return if (true) {
            // Feet
            "mapbox://styles/peakery/cjjkkx6qa63mn2rthodesuu8m"
        } else {
            // Meters
            "mapbox://styles/peakery/cjwl1k3i33nzp1dqqhuhme9av"
        }
    }

    fun getSatelliteTopoUrl(): String {
        return if (true) {
            // Feet
            "mapbox://styles/peakery/cjjkme1dj1hz02rpb9ggw2zjd"
        } else {
            // Meters
            "mapbox://styles/peakery/cjwl1jkwp46hi1csdyzgmkdpy"
        }
    }

    fun getSatelliteUrl(): String = "mapbox://styles/peakery/cjjkmis2h63yj2ss1dhq1a3z6"

    private fun selectPeak(peak: Peak) = peakSelectionRepository.selectPeak(peak)
}