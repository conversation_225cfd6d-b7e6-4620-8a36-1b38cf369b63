package com.peakery.android.feature.auth

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.webkit.WebView
import android.widget.ProgressBar
import androidx.lifecycle.Observer
import com.peakery.android.*
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.feature.webview.PeakeryWebViewClient
import org.koin.androidx.viewmodel.ext.android.viewModel

class AuthFragment: PeakeryFragment(R.layout.fragment_auth) {

    val model: AuthViewModel by viewModel()

    private lateinit var webview_button_back: View
    private lateinit var webview_button_close: View
    private lateinit var webview_loader: ProgressBar
    private lateinit var auth_webview: WebView

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        webview_button_back = requireView().findViewById(R.id.webview_button_back)
        webview_button_close = requireView().findViewById(R.id.webview_button_close)
        webview_loader = requireView().findViewById(R.id.webview_loader)
        auth_webview = requireView().findViewById(R.id.auth_webview)

        model.loginLiveData.observe(viewLifecycleOwner, Observer {
            snack("Welcome, $it!")
            activity?.finish()
        })
        initWebView(auth_webview)

        webview_button_back.setMargins(top = getStatusBarHeightPlus(R.dimen.margin_top_webview_back))
        webview_button_close.setMargins(top = getStatusBarHeightPlus(R.dimen.margin_top_webview_close))

        webview_button_close.visibility = View.VISIBLE
        webview_button_close.setOnClickListener {
            activity?.finish()
        }
        webview_button_back.setOnClickListener {
            auth_webview.goBack()
        }
    }

    fun onBackPressed(): Boolean {
        if (auth_webview.canGoBack()) {
            auth_webview.goBack()
            return true
        }
        return false
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initWebView(webview: WebView) {
        val client = PeakeryWebViewClient(requireContext())
        client.loadingComplete.observe(viewLifecycleOwner, Observer { url ->
            webview_loader.visibility = View.GONE

            if (url?.endsWith("/terms/") == true) {
                webview_button_close.visibility = View.GONE
                webview_button_back.visibility = View.VISIBLE
            } else {
                webview_button_close.visibility = View.VISIBLE
                webview_button_back.visibility = View.GONE
            }
        })

        webview.webViewClient = client
        webview.setBackgroundColor(resources.getColor(R.color.colorPrimary))
        webview.settings.javaScriptEnabled = true
        webview.loadUrl(model.getUrl(), PeakeryWebViewClient.getMobileHeaders())

        val authInterface = AuthInterface(requireContext())
        safeSubscribe(authInterface.userIdObservable.subscribe { model.onUserIdReceived(it) })
        safeSubscribe(authInterface.userNameObservable.subscribe { model.onUserNameReceived(it) })
        safeSubscribe(authInterface.userTokenObservable.subscribe { model.onUserTokenReceived(it) })
        webview.addJavascriptInterface(authInterface, "Android")

        val params = webview.layoutParams as ViewGroup.MarginLayoutParams
        params.topMargin = getStatusBarHeight()
        webview.layoutParams = params
    }
}
