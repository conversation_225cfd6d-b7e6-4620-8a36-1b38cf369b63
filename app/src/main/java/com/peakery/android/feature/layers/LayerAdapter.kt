package com.peakery.android.feature.layers

import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.inflate

class LayerAdapter(private val layers: List<Layer>,
                   private val model: LayersViewModel,
                   private val activity: PeakeryActivity): RecyclerView.Adapter<LayerAdapter.LayerViewHolder>() {

    val layerSelected = MutableLiveData<Layer>()
    private var selection = 0

    class LayerViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val image: ImageView = itemView.findViewById(R.id.layer_item_image)
        val imageSelector: View = itemView.findViewById(R.id.layer_item_image_selected)
        val title: TextView = itemView.findViewById(R.id.layer_item_text)
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        model.layerSelected.observe(activity, Observer {
            selection = layers.indexOf(it)
            notifyDataSetChanged()
        })
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): LayerViewHolder =
        LayerViewHolder(parent.inflate(R.layout.item_layer))

    override fun getItemCount(): Int = layers.size

    override fun onBindViewHolder(holder: LayerViewHolder, position: Int) {
        val layer = layers[position]

        // title
        holder.title.text = layer.name

        // image
        val imageId = activity.resources.getIdentifier(layer.image,"drawable", activity.packageName)
        holder.image.setImageResource(imageId)

        // selection
        if (position == selection) {
            holder.title.isSelected = true
            holder.imageSelector.visibility = View.VISIBLE
            holder.image.setOnClickListener {  }
        } else {
            holder.title.isSelected = false
            holder.imageSelector.visibility = View.GONE
            holder.image.setOnClickListener {
                model.onLayerSelected(layer)
                layerSelected.postValue(layer)
            }
        }
    }
}