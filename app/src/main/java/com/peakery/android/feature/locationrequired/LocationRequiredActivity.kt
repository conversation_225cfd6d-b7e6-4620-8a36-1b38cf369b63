package com.peakery.android.feature.locationrequired

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.view.View
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.feature.main.MainActivity
import com.tbruyelle.rxpermissions2.RxPermissions

class LocationRequiredActivity: PeakeryActivity() {

    private val permissions: RxPermissions = RxPermissions(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_location_required)
        val location_required_settings_button = findViewById<View>(R.id.location_required_settings_button)
        location_required_settings_button.setOnClickListener {
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
            val uri = Uri.fromParts("package", packageName, null);
            intent.data = uri
            startActivity(intent)
        }
    }

    override fun onResume() {
        super.onResume()
        if (permissions.isGranted(Manifest.permission.ACCESS_FINE_LOCATION)) {
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }
    }
}
