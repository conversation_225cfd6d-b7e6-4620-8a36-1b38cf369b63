package com.peakery.android.feature.logging.map

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.Observer
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.model.Peak
import io.reactivex.subjects.PublishSubject
import org.koin.androidx.viewmodel.ext.android.sharedViewModel

class PeakSelectionMapPeakFragment: PeakeryFragment(R.layout.fragment_map_peak) {

    val model: PeakSelectionViewModel by sharedViewModel()
    val peakSelectedObserver = PublishSubject.create<String>()

    private lateinit var map_peak_title: TextView
    private lateinit var map_peak_label_challenge: TextView
    private lateinit var map_peak_label_summits: TextView
    private lateinit var map_peak_label_classic: TextView
    private lateinit var map_peak_summary: TextView
    private lateinit var map_peak_image: ImageView
    private lateinit var map_peak_layout: ViewGroup

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initView()

        model.peakSelection.observe(viewLifecycleOwner, Observer {
            if (it.isPresent) {
                loadPeak(it.get())
            }
        })
    }

    private fun initView() {
        map_peak_title = requireView().findViewById(R.id.map_peak_title)
        map_peak_label_challenge = requireView().findViewById(R.id.map_peak_label_challenge)
        map_peak_label_summits = requireView().findViewById(R.id.map_peak_label_summits)
        map_peak_label_classic = requireView().findViewById(R.id.map_peak_label_classic)
        map_peak_summary = requireView().findViewById(R.id.map_peak_summary)
        map_peak_image = requireView().findViewById(R.id.map_peak_image)
        map_peak_layout = requireView().findViewById(R.id.map_peak_layout)
    }

    private fun loadPeak(peak: Peak) {
        map_peak_title.text = peak.name

        if (peak.challenge_count > 0) {
            map_peak_label_challenge.visibility = View.VISIBLE
            map_peak_label_challenge.text = requireContext().resources.getQuantityString(R.plurals.challenges, peak.challenge_count, peak.challenge_count)
        } else {
            map_peak_label_challenge.visibility = View.GONE
        }

        if (peak.your_summits > 0) {
            map_peak_label_summits.visibility = View.VISIBLE
            map_peak_label_summits.text = "${peak.your_summits}x"
        } else {
            map_peak_label_summits.visibility = View.GONE
        }
        if (peak.is_classic) {
            map_peak_label_classic.visibility = View.VISIBLE
        } else {
            map_peak_label_classic.visibility = View.GONE
        }

        val summits = requireContext().resources.getQuantityString(R.plurals.summits, peak.summit_count, peak.summit_count)
        map_peak_summary.text = "${model.getElevationText(peak.elevation)} • $summits"

        Glide.with(requireContext())
            .load(peak.thumbnail_url)
            .transition(DrawableTransitionOptions.withCrossFade())
            .centerCrop()
            .into(map_peak_image)

        map_peak_layout.setOnClickListener {
            peakSelectedObserver.onNext(peak.id)
        }
    }
}
