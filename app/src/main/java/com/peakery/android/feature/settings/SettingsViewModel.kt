package com.peakery.android.feature.settings

import androidx.lifecycle.ViewModel
import com.peakery.android.BuildConfig
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.core.state.StateMachine
import java.lang.IllegalStateException

class SettingsViewModel(private val userRepository: UserRepository,
                        private val peakRepository: PeakRepository,
                        private val stateMachine: StateMachine,
                        private val userPreferences: UserPreferences): ViewModel() {

    fun logout() {
        peakRepository.wipe()
        userRepository.wipe()
        stateMachine.reset()
    }

    fun getTermsUrl(): String {
        return "${BuildConfig.web_url}/terms"
    }

    fun getPrivacyUrl(): String {
        return "${BuildConfig.web_url}/privacy"
    }

    fun getDeviceInfo(): String {
        val separator = "\n"
        val builder = StringBuilder()
        builder.append("App Version: ${getAppVersion()} $separator")
        builder.append("Android Version: " + android.os.Build.VERSION.RELEASE + separator)
        builder.append("Device: " + android.os.Build.DEVICE + separator)
        builder.append("Model: " + android.os.Build.MODEL + separator)
        builder.append("Product: " + android.os.Build.PRODUCT + separator)
        builder.append("Brand: " + android.os.Build.BRAND + separator)
        builder.append("Manufacturer: " + android.os.Build.MANUFACTURER + separator)
        return builder.toString()
    }

    fun getAppVersion(): String {
        return "${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
    }

    fun getLenghtUnit(): String {
        return when (userPreferences.getLengthUnit()) {
            LengthUnit.METRIC -> "km"
            LengthUnit.IMPERIAL -> "miles"
        }
    }

    fun setUserDistanceUnit(unit: String) {
        userPreferences.setUserLengthUnit(when (unit) {
            "km" -> LengthUnit.METRIC
            "miles" -> LengthUnit.IMPERIAL
            else -> throw IllegalStateException("Invalid unit, should be set to km or miles")
        })
    }
}