package com.peakery.android.feature.logging

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import com.peakery.android.R
import com.peakery.android.core.EXTRA_FROM_TRACKING
import com.peakery.android.core.EXTRA_GPX
import com.peakery.android.core.EXTRA_PEAK
import com.peakery.android.core.FILE_PICKER_REQUEST_GPX
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.core.model.Peak
import com.peakery.android.core.repository.ImageSelectorRepository
import com.peakery.android.core.repository.REQUEST_CODE_IMAGE_SELECTION
import com.peakery.android.log
import com.peakery.android.toast
import com.zhihu.matisse.Matisse
import org.koin.android.ext.android.inject


class LoggingActivity: PeakeryActivity() {

    companion object {
        fun start(activity: Activity?, gpxFile: String? = null, fromTracking: Boolean = false) {
            val intent = Intent(activity, LoggingActivity::class.java)
            if (gpxFile != null) {
                intent.putExtra(EXTRA_GPX, gpxFile)
            }
            intent.putExtra(EXTRA_FROM_TRACKING, fromTracking)
            activity?.startActivity(intent)
        }

        fun startWithPeak(activity: Activity?, peak: Peak? = null) {
            val intent = Intent(activity, LoggingActivity::class.java)
            if (peak != null) {
                intent.putExtra(EXTRA_PEAK, peak)
            }
            activity?.startActivity(intent)
        }
    }

    private val imageSelector: ImageSelectorRepository by inject()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_manual_logging)
        supportActionBar?.title = getString(R.string.log_a_past_climb)

        val gpxFile = intent.getStringExtra(EXTRA_GPX)
        val fromTracking = intent.getBooleanExtra(EXTRA_FROM_TRACKING, false)
        if (gpxFile != null) {
            setGpxFile(gpxFile, fromTracking)
        } else {
            if (intent.type == "application/octet-stream") {
                try {
                    getPathFromURI(intent.data)?.let { setGpxFile(it, fromTracking) }
                } catch (e: Exception) {
                    e.log()
                    toast(this, "An error occurred, please try to select this file from inside the Peakery app")
                }
            } else {
                val peak = intent.getParcelableExtra<Peak>(EXTRA_PEAK)
                setManualLoggingMode(peak)
            }
        }
    }

    private fun getPathFromURI(uri: Uri?): String? {
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        val cursor = managedQuery(uri, projection, null, null, null)
        val columnIndex: Int = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
        cursor.moveToFirst()
        val path = cursor.getString(columnIndex)
        cursor.close()
        return path
    }

    private fun setGpxFile(file: String, fromTracking: Boolean) {
        val loggingFragment = supportFragmentManager.findFragmentById(R.id.fragment_manual_logging) as LoggingFragment
        loggingFragment.setGpxFile(file, fromTracking)
    }

    private fun setManualLoggingMode(peak: Peak?) {
        val loggingFragment = supportFragmentManager.findFragmentById(R.id.fragment_manual_logging) as LoggingFragment
        loggingFragment.setManualLoggingMode(peak)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when {
            requestCode == REQUEST_CODE_IMAGE_SELECTION && resultCode == RESULT_OK -> {
                imageSelector.addSelectedImages(Matisse.obtainResult(data))
            }
            requestCode == FILE_PICKER_REQUEST_GPX && resultCode == RESULT_OK -> {
            }
        }
    }
}