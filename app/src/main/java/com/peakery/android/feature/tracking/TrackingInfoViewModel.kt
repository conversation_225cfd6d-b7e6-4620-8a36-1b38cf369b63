package com.peakery.android.feature.tracking

import android.location.Location
import android.util.Log
import androidx.lifecycle.MutableLiveData
import com.peakery.android.*
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.model.Track
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.UserPreferences
import io.reactivex.Observable
import io.reactivex.functions.BiFunction
import io.reactivex.rxkotlin.subscribeBy
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.roundToInt

class TrackingInfoViewModel(tracker: Tracker,
                            private val userPreferences: UserPreferences): PeakeryViewModel() {

    val ticker = MutableLiveData<Track>()
    val position = MutableLiveData<Location>()
    val altitude = MutableLiveData<String>()
    val distance = MutableLiveData<String>()
    val paused = MutableLiveData<Boolean>()
    val gpsState = MutableLiveData<Tracker.GpsState>()

    private var trackingState: Tracker.TrackingState? = null

    init {
        safeSubscribe(tracker.timerTicker.subscribe {  ticker.postValue(it) })
        safeSubscribe(tracker.position.subscribe {
            var altitudeInMeters = it.altitude
            if (it.hasVerticalAccuracy() && it.verticalAccuracyMeters < 300) {
                if (altitudeInMeters < 0) altitudeInMeters = 0.0
                val computedAltitude = when (userPreferences.getLengthUnit()) {
                    LengthUnit.IMPERIAL -> altitudeInMeters.metersToFeet().numberFormat()
                    LengthUnit.METRIC -> altitudeInMeters.roundToInt().numberFormat()
                }

                altitude.postValue("${computedAltitude} ${userPreferences.getSmallLengthUnit()}")
            } else {
                altitude.postValue("--")
            }
            position.postValue(it)
        })
        safeSubscribe(tracker.trackObservable.subscribe {
            val computedLength = when (userPreferences.getLengthUnit()) {
                LengthUnit.IMPERIAL -> it.latLngs.computeLength().metersToMiles()
                LengthUnit.METRIC -> it.latLngs.computeLength().metersToKm()
            }

            val twoDigitsDistance = BigDecimal(computedLength).setScale(2, RoundingMode.HALF_EVEN)
            distance.postValue("$twoDigitsDistance ${userPreferences.getLargeLengthUnit()}")
        })
        safeSubscribe(tracker.trackingState.subscribe { state ->
            trackingState = state
            paused.postValue(state == Tracker.TrackingState.TRACKING_PAUSED)
        })
        safeSubscribe(tracker.gpsState.subscribeBy(
            onNext = {
                if (trackingState == Tracker.TrackingState.TRACKING_NOT_STARTED) {
                    gpsState.postValue(it)
                } else {
                    gpsState.postValue(Tracker.GpsState.ACQUIRED)
                }
            }
        ))
    }
}