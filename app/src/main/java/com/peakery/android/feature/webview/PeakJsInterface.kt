package com.peakery.android.feature.webview

import android.webkit.JavascriptInterface
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.PeakDeserializer
import com.peakery.android.log
import io.reactivex.subjects.PublishSubject

/**
 * Interfaces with Peakery web page and intercepts Android.peakCoordinate(...)
 */
class PeakJsInterface {

    val webMapSelected = PublishSubject.create<Peak>()
    val loggingSelected = PublishSubject.create<Peak>()
    val goBack = PublishSubject.create<Boolean>()

    private val gson by lazy { GsonBuilder().registerTypeAdapter(Peak::class.java, PeakDeserializer()).create() }

    @JavascriptInterface
    fun peakInfo(json: String) {
        try {
            val fixedJson = json.replace("\"None\"", "0.0")
            val peak = gson.fromJson(fixedJson, Peak::class.java)
            webMapSelected.onNext(peak)
        } catch (e: Exception) {
            e.log()
        }
    }

    @JavascriptInterface
    fun logClimb(json: String) {
        try {
            val fixedJson = json.replace("\"None\"", "0.0")
            val peak = gson.fromJson(fixedJson, Peak::class.java)
            loggingSelected.onNext(peak)
        } catch (e: Exception) {
            e.log()
        }
    }

    @JavascriptInterface
    fun goBack() {
        goBack.onNext(true)
    }
}