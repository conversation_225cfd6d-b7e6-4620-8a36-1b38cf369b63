package com.peakery.android.feature.logging

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.peakery.android.R
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.model.formattedLocation
import com.peakery.android.core.model.isPeak
import com.peakery.android.inflate
import io.reactivex.subjects.PublishSubject

class LoggingPeakSearchAdapter(
    private val suggestions: List<Suggestion>,
    private val context: Context,
    val model: LoggingViewModel
): RecyclerView.Adapter<LoggingPeakSearchAdapter.SearchBoxViewHolder>() {

    var observer = PublishSubject.create<Suggestion>()

    class SearchBoxViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val layout: ViewGroup = itemView.findViewById(R.id.item_suggestion_layout)
        val name: TextView = itemView.findViewById(R.id.item_suggestion_name)
        val image: ImageView = itemView.findViewById(R.id.item_suggestion_image)
        val location: TextView = itemView.findViewById(R.id.item_suggestion_location)
        val extras: TextView = itemView.findViewById(R.id.item_suggestion_extras)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchBoxViewHolder {
        val view = parent.inflate(R.layout.item_logging_peak)
        return SearchBoxViewHolder(view)
    }

    override fun getItemCount(): Int = suggestions.size

    override fun onBindViewHolder(holder: SearchBoxViewHolder, position: Int) {
        val suggestion = suggestions[position]
        holder.name.text = suggestion.name

        Glide.with(context)
            .load(suggestion.thumbnail_url)
            .transition(DrawableTransitionOptions.withCrossFade())
            .centerCrop()
            .into(holder.image)

        val regionName = suggestion.region?.firstOrNull()?.region_name?: ""
        val countryName = suggestion.country?.firstOrNull()?.country_name?:
        suggestion.region?.firstOrNull()?.country_name?: ""
        holder.location.text = if (regionName.isNotEmpty()) {
            "$regionName, $countryName"
        } else { "" }
        holder.location.text

        val summits = context.resources.getQuantityString(R.plurals.summits, suggestion.summit_count, suggestion.summit_count)
        holder.extras.text = "${model.getElevationText(suggestion.elevation)} • $summits"
        holder.extras.visibility = View.VISIBLE

        holder.layout.setOnClickListener { observer.onNext(suggestion) }
    }
}