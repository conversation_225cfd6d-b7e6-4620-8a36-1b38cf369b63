package com.peakery.android.feature.tracking

import android.animation.ArgbEvaluator
import android.animation.ObjectAnimator
import android.os.Bundle
import android.transition.TransitionManager
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.lifecycle.Observer
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.location.Tracker
import com.peakery.android.getFormattedTime
import org.koin.androidx.viewmodel.ext.android.viewModel

class TrackingInfoFragment: PeakeryFragment(R.layout.fragment_tracking_info) {

    companion object { fun new() : TrackingInfoFragment = TrackingInfoFragment() }

    private val model: TrackingInfoViewModel by viewModel()

    private var pauseAnimator: ObjectAnimator? = null

    private lateinit var tracker_row_time: TextView
    private lateinit var tracker_row_elevation: TextView
    private lateinit var tracker_row_distance: TextView
    private lateinit var tracker_row_root: ViewGroup
    private lateinit var tracker_row_pause_state: View
    private lateinit var tracker_row_pause_state_text: TextView

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        initView()

        model.ticker.observe(viewLifecycleOwner, Observer { track ->
            tracker_row_time.text = track.elapsedTime.getFormattedTime()
        })
        model.altitude.observe(viewLifecycleOwner, Observer { altitude ->
            tracker_row_elevation.text = altitude
        })
        model.distance.observe(viewLifecycleOwner, Observer { distance ->
            tracker_row_distance.text = distance
        })
        model.paused.observe(viewLifecycleOwner, Observer { paused ->
            showPauseStateBar(paused)
        })
        model.gpsState.observe(viewLifecycleOwner, Observer { gpsState ->
            when (gpsState) {
                Tracker.GpsState.ACQUIRED -> showGpsStateBar(false)
                Tracker.GpsState.ACQUIRING -> showGpsStateBar(true, R.string.acquiring_gps)
                Tracker.GpsState.NOT_FOUND -> showGpsStateBar(true, R.string.gps_not_found)
            }
        })
    }

    private fun initView() {
        tracker_row_time = requireView().findViewById(R.id.tracker_row_time)
        tracker_row_elevation = requireView().findViewById(R.id.tracker_row_elevation)
        tracker_row_distance = requireView().findViewById(R.id.tracker_row_distance)
        tracker_row_root = requireView().findViewById(R.id.tracker_row_root)
        tracker_row_pause_state = requireView().findViewById(R.id.tracker_row_pause_state)
        tracker_row_pause_state_text = requireView().findViewById(R.id.tracker_row_pause_state_text)
    }

    private fun showPauseStateBar(show: Boolean) {
        if (show) {
            TransitionManager.beginDelayedTransition(tracker_row_root)
            if (pauseAnimator == null) {
                pauseAnimator = ObjectAnimator.ofObject(
                    tracker_row_pause_state,
                    "backgroundColor",
                    ArgbEvaluator(),
                    resources.getColor(R.color.orange_pulse_start),
                    resources.getColor(R.color.orange_pulse_end)
                ).apply {
                    duration = 1500
                    startDelay = 1000
                    repeatCount = ObjectAnimator.INFINITE
                    repeatMode = ObjectAnimator.REVERSE
                }
            }
            pauseAnimator?.start()
            tracker_row_pause_state.visibility = View.VISIBLE
        } else {
            tracker_row_pause_state.visibility = View.GONE
            pauseAnimator?.end()
        }
    }

    private fun showGpsStateBar(show: Boolean, @StringRes stringId: Int? = null) {
        if (show) {
            TransitionManager.beginDelayedTransition(tracker_row_root)
            if (pauseAnimator == null) {
                pauseAnimator = ObjectAnimator.ofObject(
                    tracker_row_pause_state,
                    "backgroundColor",
                    ArgbEvaluator(),
                    resources.getColor(R.color.orange_pulse_start),
                    resources.getColor(R.color.orange_pulse_end)
                ).apply {
                    duration = 1500
                    startDelay = 1000
                    repeatCount = ObjectAnimator.INFINITE
                    repeatMode = ObjectAnimator.REVERSE
                }
            }
            pauseAnimator?.start()
            if (stringId != null) {
                tracker_row_pause_state_text.text = getString(stringId)
            }
            tracker_row_pause_state.visibility = View.VISIBLE
        } else {
            tracker_row_pause_state.visibility = View.GONE
            pauseAnimator?.end()
        }
    }
}

