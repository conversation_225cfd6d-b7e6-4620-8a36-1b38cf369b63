package com.peakery.android.feature.logging

import android.Manifest
import android.content.Context
import android.os.Build
import androidx.lifecycle.MutableLiveData
import com.peakery.android.BuildConfig
import com.peakery.android.core.base.PeakeryViewModel
import com.peakery.android.core.db.LocalDao
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.network.S3Uploader
import com.peakery.android.core.network.S3_AUTH_TOKEN
import com.peakery.android.core.network.S3_BUCKET_NAME
import com.peakery.android.core.network.UploadService
import com.peakery.android.core.repository.ImageSelectorRepository
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.repository.SelectedImage
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.logging.map.PeakSelectionRepository
import com.peakery.android.log
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.rxkotlin.subscribeBy
import io.reactivex.schedulers.Schedulers
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import org.json.JSONArray
import org.json.JSONObject
import retrofit2.Response
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID

class LoggingViewModel(private val peakRepository: PeakRepository,
                       private val uploadService: UploadService,
                       private val s3Uploader: S3Uploader,
                       private val dao: LocalDao,
                       private val imageSelector: ImageSelectorRepository,
                       private val context: Context,
                       private val userPreferences: UserPreferences,
                       private val peakSelectionRepository: PeakSelectionRepository,
                       private val userRepository: UserRepository,
                       private val stateMachine: StateMachine,
                       private val tracker: Tracker): PeakeryViewModel() {

    val selectedPeaks = MutableLiveData<SelectedPeaks>()
    val selectedImages = MutableLiveData<List<SelectedImage>>()
    val error = MutableLiveData<LoggingError>()
    val submission = MutableLiveData<Submission>()
    val suggestionsResults = MutableLiveData<List<Suggestion>>()
    val suggestionsLoading = MutableLiveData<Boolean>()

    private val attempts = mutableSetOf<String>()

    private val locale: Locale = context.resources.configuration.locale
    private val displayDateFormat: SimpleDateFormat = SimpleDateFormat("MMM dd, yyyy", locale)
    private val serverDateFormat: SimpleDateFormat = SimpleDateFormat("yyyy-MM-dd", locale)

    private var gpxFileName: String? = null

    init {
        safeSubscribe(imageSelector.selectedImagesObservable.subscribe { selectedImages.postValue(it) })
        safeSubscribe(peakSelectionRepository.selectedPeaks.subscribe { selectedPeaks.postValue(SelectedPeaks(false, it)) })
    }

    override fun onCleared() {
        peakSelectionRepository.clear()
        super.onCleared()
    }

    fun epochToDisplayDate(epoch: Long): String {
        return displayDateFormat.format(Date(epoch))
    }

    fun selectPeak(peak: Peak) = peakSelectionRepository.selectPeak(peak)

    fun unselectPeak(peak: Peak) = peakSelectionRepository.unselectPeak(peak)

    fun getReadStoragePermission(): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }
    }

    fun deleteImage(selectedImage: SelectedImage) {
        imageSelector.deleteImage(selectedImage)
    }

    fun getElevationText(elevation: Double): String {
        return userPreferences.getSmallUnitFromFeet(elevation)
    }

    fun setGpxFile(filePath: String) {
        selectedPeaks.postValue(SelectedPeaks(true))

        val file = File(filePath)
        safeSubscribe(
            getS3SignedUrlObservable()
                .flatMap {
                    gpxFileName = it.fileName
                    getGpxUploadObservable(it.url, file)
                }
                .flatMap { getParseGpxObservable(gpxFileName!!) }
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeBy(
                    onNext = { peaks ->
                        peakSelectionRepository.selectPeak(*peaks.toTypedArray())
                        if (peaks.isEmpty()) {
                            error.postValue(LoggingError.NoPeaksFound)
                        }
                    },
                    onError = {
                        selectedPeaks.postValue(SelectedPeaks(false))
                        if (it is LoggingError) {
                            error.postValue(it)
                        } else {
                            error.postValue(LoggingError.Unknown)
                        }

                        it.log()
                    }
                )
        )
    }

    private fun getS3SignedUrlObservable(): Observable<SignedUrlData> {
        val generatedFileName = UUID.randomUUID().toString() + ".gpx"

        val signedUrlParams = JSONObject()
        signedUrlParams.put("auth_token", S3_AUTH_TOKEN)
        signedUrlParams.put("bucket_name", S3_BUCKET_NAME)
        signedUrlParams.put("content_type", "application/xml")
        signedUrlParams.put("object_name", "gpx/$generatedFileName")

        return uploadService.getSignedUploadUrl(signedUrlParams.toString())
            .subscribeOn(Schedulers.io())
            .map { SignedUrlData(it.body()!!.data.url, generatedFileName) }
            .retry(3)
            .doOnError { throw LoggingError.GpxUploadError }
    }

    private fun getGpxUploadObservable(url: String, file: File): Observable<Response<Void>> {
        val gpxBody = RequestBody.create("application/xml".toMediaTypeOrNull(), file)
        return s3Uploader.upload(url, gpxBody)
            .subscribeOn(Schedulers.io())
            .retry(3)
            .doOnError { throw LoggingError.SignedUrlError }
    }

    private fun getParseGpxObservable(fileName: String): Observable<List<Peak>> {
        return peakRepository.parseGpx(fileName)
            .subscribeOn(Schedulers.io())
            .map { it.body()!!.data.peaks }
            .doOnError { throw LoggingError.ParseGpxError }
    }

    fun searchPeaks(keywords: String) {
        suggestionsLoading.postValue(true)
        safeSubscribe(
            Observable.just(0)
                .switchMap { peakRepository.getPeakSuggestions(keywords) }
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(
                    { response ->
                        suggestionsLoading.postValue(false)
                        if (response.isSuccessful) {
                            suggestionsResults.postValue(response.body()!!.data.suggestions)
                            stateMachine.reset()
                        }
                    },
                    { suggestionsLoading.postValue(false) }
                )
        )
    }

    fun getLengthUnit(): LengthUnit {
        return userPreferences.getLengthUnit()
    }

    fun submit(notes: String, date: String) {
        submission.postValue(Submission.Loading)

        val trackUploadParams = JSONObject()

        trackUploadParams.put("auth_token", userRepository.getUserToken())
        trackUploadParams.put("track_notes", notes)
        trackUploadParams.put("photos", imageSelector.getUploadedUuidJsonArray())
        trackUploadParams.put("peaks", getPeaksJsonArray())

        if (date.isNotEmpty()) {
            val displayDate = displayDateFormat.parse(date)
            trackUploadParams.put("log_date", serverDateFormat.format(displayDate))
        }

        if (gpxFileName != null) {
            trackUploadParams.put("gpx_filename", gpxFileName)
        }

        safeSubscribe(
            uploadService.uploadClimb(trackUploadParams.toString())
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeBy(
                    onNext = {
                        if (it.isSuccessful) {
                            tracker.saveTrack()
                            submission.postValue(Submission.Success(
                                "${BuildConfig.web_url}${it.body()!!.data.summit_url}"
                            ))
                        } else {
                            submission.postValue(Submission.Error)
                        }
                    },
                    onError = {
                        submission.postValue(Submission.Error)
                    }
                )
        )
    }

    private fun getPeaksJsonArray(): JSONArray {
        val array = JSONArray()
        selectedPeaks.value?.data?.forEach {
            val json = JSONObject()
            json.put("id", it.id)
            json.put("attempt", attempts.contains(it.id))
            array.put(json)
        }
        return array
    }

    fun setSummited(peakId: String) {
        attempts.remove(peakId)
    }

    fun setAttempted(peakId: String) {
        attempts.add(peakId)
    }

    fun deleteTrack() {
        stateMachine.reset()
        tracker.deleteTrack()
    }
}

data class SignedUrlData(
    val url: String,
    val fileName: String
)

sealed class Submission {
    object Loading: Submission()
    data class Success(val url: String): Submission()
    object Error: Submission()
}

sealed class LoggingError: Error() {
    object SignedUrlError : LoggingError()
    object GpxUploadError : LoggingError()
    object ParseGpxError : LoggingError()
    object NoPeaksFound : LoggingError()
    object Unknown : LoggingError()
}

data class SelectedPeaks(
    val isLoading: Boolean,
    val data: List<Peak> = emptyList()
)