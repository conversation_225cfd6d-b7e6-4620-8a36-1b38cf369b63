package com.peakery.android.feature.main

import android.content.Intent
import android.content.res.Configuration
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updateLayoutParams
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.UpdateAvailability
import com.jakewharton.rxbinding2.view.layoutChanges
import com.peakery.android.*
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.core.location.TrackerService
import com.peakery.android.core.repository.ImageSelectorRepository
import com.peakery.android.core.repository.REQUEST_CODE_IMAGE_SELECTION
import com.peakery.android.core.state.StateMachine
import com.peakery.android.feature.locationrequired.LocationRequiredActivity
import com.peakery.android.feature.map.MapFragment
import com.peakery.android.feature.peaks.PeakListFragment
import com.peakery.android.feature.searchbox.SearchBoxFragment
import com.peakery.android.feature.tabs.TabsFragment
import com.peakery.android.feature.tracking.TrackingControlsFragment
import com.peakery.android.feature.tracking.TrackingInfoFragment
import com.peakery.android.feature.webview.WebViewFragment
import com.tbruyelle.rxpermissions2.RxPermissions
import com.zhihu.matisse.Matisse
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel

private const val REQUEST_CODE_APP_UPDATE = 341234

class MainActivity : PeakeryActivity() {

    private val permissions: RxPermissions = RxPermissions(this)
    private val model: MainViewModel by viewModel()
    private val imageSelector: ImageSelectorRepository by inject()
    private val uiCoordinator: UiCoordinator by inject()
    private val appUpdateManager: AppUpdateManager by inject()

    private val fragments = HashMap<String, Fragment>()
    private lateinit var mainLayoutBottom: ViewGroup

    private var permissionRequestCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        initStateMachine()

        mainLayoutBottom = findViewById(R.id.main_layout_bottom)
        safeSubscribe(mainLayoutBottom.layoutChanges().subscribe {
            uiCoordinator.updateBottomContentHeight(mainLayoutBottom.height.toFloat())
        })
        findViewById<View>(R.id.main_layout_top_content).setMargins(top = getStatusBarHeightPlus(R.dimen.margin_top_content))

        logHeapSize()
        checkUpdates()
    }

    private fun setTransparentStatusBar(state: StateMachine.State) {
        val currentNightMode = resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK

        val forceLight = when (state) {
            StateMachine.State.EXPLORE_MAP,
            StateMachine.State.TRACKING -> true
            else -> false
        }

        enableEdgeToEdge(
            navigationBarStyle = when (currentNightMode) {
                Configuration.UI_MODE_NIGHT_NO -> SystemBarStyle.light(Color.WHITE, Color.BLACK)
                Configuration.UI_MODE_NIGHT_YES -> SystemBarStyle.dark(getColor(R.color.dark_navigation_bar))
                else -> error("Illegal State, current mode is $currentNightMode")
            },
            statusBarStyle = when (state) {
                StateMachine.State.EXPLORE_MAP,
                StateMachine.State.TRACKING -> SystemBarStyle.light(Color.TRANSPARENT, Color.TRANSPARENT)
                StateMachine.State.EXPLORE_LIST -> SystemBarStyle.dark(getColor(R.color.list_background))
                else -> SystemBarStyle.dark(Color.BLACK)
            }
        )
        ViewCompat.setOnApplyWindowInsetsListener(mainLayoutBottom) { v, windowInsets ->
            val insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars())
            v.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                leftMargin = insets.left
                bottomMargin = insets.bottom
                rightMargin = insets.right
            }

            WindowInsetsCompat.CONSUMED
        }
    }

    private fun checkUpdates() {
        val task = appUpdateManager.appUpdateInfo
        task.addOnSuccessListener {
            if (it.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                promptUpdate(it)
            }
        }
    }

    private fun promptUpdate(appUpdateInfo: AppUpdateInfo) {
        when {
            appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.IMMEDIATE) && appUpdateInfo.updatePriority() >= 4 -> {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.IMMEDIATE,
                    this,
                    REQUEST_CODE_APP_UPDATE
                )
            }
            appUpdateInfo.isUpdateTypeAllowed(AppUpdateType.FLEXIBLE) -> {
                appUpdateManager.startUpdateFlowForResult(
                    appUpdateInfo,
                    AppUpdateType.FLEXIBLE,
                    this,
                    REQUEST_CODE_APP_UPDATE
                )
            }
        }
    }

    // Temporary logging, remove me
    private fun logHeapSize() {
        val mbMemoryAvailable = Runtime.getRuntime().maxMemory() / 1000000
        Log.e("logHeapSize", "logHeapSize: $mbMemoryAvailable mb")
        MemoryLogging("$mbMemoryAvailable mb").log()
    }

    override fun onResume() {
        super.onResume()
        initPermissions()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        for (fragment in supportFragmentManager.fragments) {
            fragment.onActivityResult(requestCode, resultCode, data)
        }
        if (requestCode == REQUEST_CODE_IMAGE_SELECTION && resultCode == RESULT_OK) {
            imageSelector.addSelectedImages(Matisse.obtainResult(data))
        }
    }

    private fun initStateMachine() {
        addFragment(R.id.main_layout_map, getMapFragment())
        addFragment(R.id.main_layout_profile, getProfileFragment())
        addFragment(R.id.main_layout_latest, getLatestFragment())
        model.data.observe(this, Observer {
            setTransparentStatusBar(it.state)
            when (it.state) {
                StateMachine.State.TRACKING -> {
                    removeFragment(getPeakListFragment())
                    hideFragment(getLatestFragment())
                    hideFragment(getProfileFragment())
                    replaceFragmentWithAnimation(
                        R.id.main_layout_top_content,
                        TrackingInfoFragment.new(),
                        "top_content"
                    )
                    addFragmentWithSlideAnimation(
                        R.id.main_layout_bottom_content,
                        TrackingControlsFragment.new(),
                        "bottom_content"
                    )
                    removeFragment(getTabsFragment())
                }
                StateMachine.State.EXPLORE_MAP -> {
                    removeFragment(getPeakListFragment())
                    hideFragment(getLatestFragment())
                    hideFragment(getProfileFragment())
                    replaceFragmentWithAnimation(
                        R.id.main_layout_top_content,
                        getNewSearchBoxFragment(),
                        "top_content"
                    )
                    removeFragment("bottom_content")
                    addFragment(R.id.main_layout_bottom_tabs, getTabsFragment())
                }
                StateMachine.State.EXPLORE_LIST -> {
                    hideFragment(getLatestFragment())
                    hideFragment(getProfileFragment())
                    replaceFragmentWithAnimation(
                        R.id.main_layout_top_content,
                        getNewSearchBoxFragment(),
                        "top_content"
                    )
                    addFragmentWithFadeAnimation(
                        R.id.main_layout_list,
                        getPeakListFragment(),
                        "content"
                    )
                    removeFragment("bottom_content")
                    addFragment(R.id.main_layout_bottom_tabs, getTabsFragment())
                }
                StateMachine.State.LATEST -> {
                    removeFragment("top_content")
                    hideFragment(getProfileFragment())
                    addFragment(R.id.main_layout_latest, getLatestFragment(), "content")
                    removeFragment("bottom_content")
                    addFragment(R.id.main_layout_bottom_tabs, getTabsFragment())
                    showFragment(getLatestFragment())
                }
                StateMachine.State.PROFILE -> {
                    removeFragment("top_content")
                    hideFragment(getLatestFragment())
                    addFragment(R.id.main_layout_profile, getProfileFragment(), "content")
                    removeFragment("bottom_content")
                    addFragment(R.id.main_layout_bottom_tabs, getTabsFragment())
                    showFragment(getProfileFragment())
                }
            }
        })
    }

    private fun getNewSearchBoxFragment(): Fragment {
        val key = "searchbox"
        val fragment = SearchBoxFragment.new()
        fragments[key] = fragment
        return fragment
    }

    private fun getSearchBoxFragment(): SearchBoxFragment? {
        val key = "searchbox"
        return if (fragments.containsKey(key)) {
            fragments[key]!! as SearchBoxFragment
        } else {
            null
        }
    }

    private fun getMapFragment(): Fragment {
        val key = "map"
        return if (fragments.containsKey(key)) {
            fragments[key]!!
        } else {
            val fragment = MapFragment.new()
            fragments[key] = fragment
            fragment
        }
    }

    private fun getPeakListFragment(): Fragment {
        val key = "peak_list"
        return if (fragments.containsKey(key)) {
            fragments[key]!!
        } else {
            val fragment = PeakListFragment.new()
            fragments[key] = fragment
            fragment
        }
    }

    private fun getLatestFragment(): WebViewFragment {
        val key = "latest"
        return if (fragments.containsKey(key)) {
            fragments[key]!! as WebViewFragment
        } else {
            val fragment = WebViewFragment.new(StateMachine.State.LATEST)
            fragments[key] = fragment
            fragment
        }
    }

    private fun getProfileFragment(): WebViewFragment {
        val key = "profile"
        return if (fragments.containsKey(key)) {
            fragments[key]!! as WebViewFragment
        } else {
            val fragment = WebViewFragment.new(StateMachine.State.PROFILE)
            fragments[key] = fragment
            fragment
        }
    }

    private fun getTabsFragment(): Fragment {
        val key = "tabs"
        return if (fragments.containsKey(key)) {
            fragments[key]!!
        } else {
            val fragment = TabsFragment.new()
            fragments[key] = fragment
            fragment
        }
    }

    private fun initPermissions() {
        if (permissionRequestCount > 3) {
            val intent = Intent(this, LocationRequiredActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK.and(Intent.FLAG_ACTIVITY_SINGLE_TOP)
            startActivity(intent)
            finish()
            return
        }

        safeSubscribe(
            permissions.request(model.getRequestedPermissions()).subscribe({ granted ->
                if (granted) {
                    permissionRequestCount = 0
                    startService(Intent(this, TrackerService::class.java))
                } else {
                    permissionRequestCount += 1
                }
            },
                { it.log() })
        )
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            var override = when (model.data.value?.state) {
                StateMachine.State.EXPLORE_MAP, StateMachine.State.EXPLORE_LIST -> {
                    val searchBoxFragment = getSearchBoxFragment()
                    if (searchBoxFragment != null && searchBoxFragment.canGoBack()) {
                        searchBoxFragment.goBack()
                        true
                    }
                    false
                }
                StateMachine.State.LATEST -> getLatestFragment().onBackPressed()
                StateMachine.State.PROFILE -> getProfileFragment().onBackPressed()
                else -> false
            }
            if (override) {
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }
}

class MemoryLogging(str: String): Error(str)
