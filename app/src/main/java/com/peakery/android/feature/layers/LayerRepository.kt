package com.peakery.android.feature.layers

import com.peakery.android.core.location.CountryDetector
import io.reactivex.subjects.BehaviorSubject

class LayerRepository(private val countryDetector: CountryDetector) {

    val allLayers = listOf(
        Layer("Natural Atlas (US)", "natural-atlas", "natural-atlas-layer", "layer_natural_atlas", arrayOf("https://a-naturalatlas-tiles.global.ssl.fastly.net/topo/{z}/{x}/{y}/<EMAIL>")),
        Layer("OpenTopoMap", "opentopo", "opentopo-layer", "layer_opentopo",arrayOf("https://a.tile.opentopomap.org/{z}/{x}/{y}.png", "https://b.tile.opentopomap.org/{z}/{x}/{y}.png", "https://c.tile.opentopomap.org/{z}/{x}/{y}.png")),
        Layer("Terrain", "terrain", "terrain-layer", "layer_terrain", arrayOf("")),
        Layer("Topo (US/CA/NZ)", "caltopo", "caltopo-layer", "layer_caltopo", arrayOf("https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png")),
        Layer("Satellite", "satellite", "satellite-layer", "layer_satellite",arrayOf("")),
        Layer("Satellite Topo", "satellite-topo", "satellite-topo-layer", "layer_satellite_topo", arrayOf(""))
    )

    val layerObservable = getDefaultLayer()

    fun onLayerSelected(layer: Layer) {
        layerObservable.onNext(layer)
    }

    private fun getDefaultLayer(): BehaviorSubject<Layer> {
        return if (countryDetector.getDeviceCountryCode().isUnitedStates()) {
            BehaviorSubject.createDefault(allLayers[0])
        } else {
            BehaviorSubject.createDefault(allLayers[1])
        }
    }
}