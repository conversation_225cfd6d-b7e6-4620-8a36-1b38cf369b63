package com.peakery.android.feature.map

import android.graphics.Color
import android.os.Bundle
import android.widget.TextView
import androidx.lifecycle.Observer
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryFragment
import org.koin.androidx.viewmodel.ext.android.viewModel

class LocationDebugFragment(): PeakeryFragment(R.layout.fragment_location_debug) {

    private val model: LocationDebugViewModel by viewModel()

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        val debug_longitude = requireView().findViewById<TextView>(R.id.debug_longitude)
        val debug_latitude = requireView().findViewById<TextView>(R.id.debug_latitude)
        val debug_accuracy = requireView().findViewById<TextView>(R.id.debug_accuracy)
        val debug_altitude = requireView().findViewById<TextView>(R.id.debug_altitude)
        val debug_validity = requireView().findViewById<TextView>(R.id.debug_validity)
        val debug_reasons = requireView().findViewById<TextView>(R.id.debug_reasons)

        model.debugLocation.observe(viewLifecycleOwner, Observer { debugLocation ->
            val location = debugLocation.location
            debug_longitude.text = "lon:   ${location.longitude}"
            debug_latitude.text = "lat:   ${location.latitude}"
            debug_accuracy.text = "acc:   ${location.accuracy}"
            debug_altitude.text = "wgs84: ${model.getAltitudeForUserPreference(debugLocation.wgs84Elevation)}"

            if (debugLocation.filters.isEmpty()) {
                debug_validity.text = "VALID"
                debug_validity.setTextColor(Color.GREEN)
                debug_reasons.text = ""
            } else {
                debug_validity.text = "INVALID"
                debug_validity.setTextColor(Color.RED)
                debug_reasons.text = debugLocation.filters.toString()
            }
        })
    }
}
