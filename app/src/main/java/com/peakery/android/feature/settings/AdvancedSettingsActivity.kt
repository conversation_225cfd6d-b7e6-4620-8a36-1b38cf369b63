package com.peakery.android.feature.settings

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.peakery.android.R
import com.peakery.android.core.base.PeakeryActivity

class AdvancedSettingsActivity: PeakeryActivity() {

    companion object { fun start(activity: Activity?) = activity?.startActivity(Intent(activity, AdvancedSettingsActivity::class.java)) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_advanced_settings)
        supportActionBar?.title = getString(R.string.advanced)
    }
}