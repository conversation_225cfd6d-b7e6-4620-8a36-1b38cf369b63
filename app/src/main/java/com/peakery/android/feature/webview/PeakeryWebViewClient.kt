package com.peakery.android.feature.webview

import android.annotation.SuppressLint
import android.app.DownloadManager
import android.content.Context
import android.content.Context.DOWNLOAD_SERVICE
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Environment
import android.util.Log
import android.webkit.*
import androidx.lifecycle.MutableLiveData
import com.google.firebase.crashlytics.FirebaseCrashlytics
import org.koin.core.qualifier.named

class PeakeryWebViewClient(private val context: Context): WebViewClient() {

    companion object {
        fun getMobileHeaders(): HashMap<String, String> {
            val headers = hashMapOf<String, String>()
            headers["PEAKMOBILEAPP"] = "com.peakery.android"
            return headers
        }
    }

    var loadingStart = MutableLiveData<Boolean>()
    var loadingComplete = MutableLiveData<String?>()
    var loadingError = MutableLiveData<Any?>()

    private var lastValidUrl: String = ""

    override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
        val host = Uri.parse(url).host!!
        if (url.matches(Regex(".*reload"))) {
            if (lastValidUrl.isEmpty()) {
                Log.d("PeakeryWebViewClient", "reload: goBack")
                view.goBack()
            } else {
                Log.d("PeakeryWebViewClient", "reload: loadUrl: $url")
                view.loadUrl(lastValidUrl, getMobileHeaders())
            }
            loadingStart.postValue(true)
        } else if (host.contains("peakery.com") || host.contains("peakerydev.herokuapp")) {
            lastValidUrl = url
            view.loadUrl(url, getMobileHeaders())
            loadingStart.postValue(true)
        } else {
            if (url.endsWith(".gpx")) {
                downloadFile(url)
            } else {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                view.context.startActivity(intent)
            }
        }
        return true
    }

    private fun downloadFile(url: String) {
        val fileName = url.split("/").last()
        val request = DownloadManager.Request(Uri.parse(url)).apply {
            setTitle(fileName)
            named(fileName)
            setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, fileName)
            setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        }

        val downloadManager = context.getSystemService(DOWNLOAD_SERVICE) as DownloadManager
        downloadManager.enqueue(request)
    }

    override fun onPageCommitVisible(view: WebView?, url: String?) {
        super.onPageCommitVisible(view, url)
        loadingComplete.postValue(url)
    }

    override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
        super.onPageStarted(view, url, favicon)
        loadingStart.postValue(true)
    }

    override fun onReceivedHttpError(view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?) {
        super.onReceivedHttpError(view, request, errorResponse)
        Log.e("PeakeryWebViewClient", "onReceiveHttpError: ${errorResponse?.statusCode} for ${request?.url}")

        if (!request?.url.toString().contains("peakery.com")) return

        when (errorResponse?.statusCode) {
            400, 500 -> loadingError.postValue(errorResponse)
        }
    }

    @SuppressLint("NewApi")
    override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
        // Lots of different things can cause this callback, including images failing to load
        // Since this is set too aggressively, turn off client error handling and report a firebase
        // error to be able to identify in which cases we should invoke loadingError
        //
        // loadingError.postValue(error)
        try { Log.e("PeakeryWebViewClient", "onReceivedError: ${error?.description} + ${request!!.url}") }
        catch (ignore: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ignore)
        }
    }
}
