package com.peakery.android.feature.history

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.core.content.FileProvider
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.BuildConfig
import com.peakery.android.R
import com.peakery.android.core.EXTRA_TRACK_ID
import com.peakery.android.core.base.PeakeryActivity
import com.peakery.android.core.model.Track
import com.peakery.android.log
import com.peakery.android.toast
import com.tbruyelle.rxpermissions2.RxPermissions
import io.reactivex.rxkotlin.subscribeBy
import org.koin.androidx.viewmodel.ext.android.viewModel

class HistoryActivity : PeakeryActivity() {

    companion object { fun start(activity: Activity?) = activity?.startActivity(Intent(activity, HistoryActivity::class.java)) }

    private val model: HistoryViewModel by viewModel()
    private val permissions: RxPermissions = RxPermissions(this)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_history)
        val history_list = findViewById<RecyclerView>(R.id.history_list)

        supportActionBar?.title = getString(R.string.track_recovery_title)

        model.data.observe(this, Observer {
            val adapter = HistoryAdapter(it.reversed())
            history_list.adapter = adapter
            safeSubscribe(adapter.observer.subscribe { historyData ->
                onTrackSelected(historyData.track)
            })
            safeSubscribe(adapter.shareObserver.subscribe { historyData ->
                processGpx(historyData.track)
            })
        })

        model.loadHistory()
    }

    private fun processGpx(track: Track) {
        safeSubscribe(permissions.request(model.getRequestedPermissions()).subscribe {
            if (it == true) {
                showProgressDialog(getString(R.string.processing_data))
                safeSubscribe(model.getGpx(track, this).subscribeBy(
                    onNext = {
                        dismissProgressDialog()
                        if (it.success) {
                            val contentUri = FileProvider.getUriForFile(this, "${BuildConfig.APPLICATION_ID}.provider", it.file!!)

                            val shareIntent = Intent(Intent.ACTION_SEND)
                            shareIntent.type = "*/*"
                            shareIntent.putExtra(Intent.EXTRA_STREAM, contentUri)
                            shareIntent.putExtra(Intent.EXTRA_SUBJECT,"My Peakery GPX Track")
                            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION) // temp permission for receiving app to read this file
                            startActivity(Intent.createChooser(shareIntent, "Share GPX File"))

                            toast(this, "GPX File created")
                        } else {
                            toast(this, "GPX File creation failed")
                        }
                    },
                    onError = {
                        toast(this, "GPX File creation failed")
                        it.log()
                    }
                ))
            } else {
                toast(this, getString(R.string.write_permission_required))
            }
        })
    }

    private fun onTrackSelected(track: Track) {
        val intent = Intent(this, HistoryDetailActivity::class.java)
        intent.putExtra(EXTRA_TRACK_ID, track.id)
        startActivity(intent)
    }
}
