package com.peakery.android.feature.searchbox

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.peakery.android.R
import com.peakery.android.core.model.Suggestion
import com.peakery.android.core.model.formattedLocation
import com.peakery.android.core.model.isPeak
import com.peakery.android.inflate
import com.peakery.android.numberFormat
import io.reactivex.subjects.PublishSubject

class SearchBoxAdapter(
    private val suggestions: List<Suggestion>,
    private val context: Context,
    val model: SearchBoxViewModel
): RecyclerView.Adapter<SearchBoxAdapter.SearchBoxViewHolder>() {

    var observer = PublishSubject.create<Suggestion>()

    class SearchBoxViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val layout: ViewGroup = itemView.findViewById(R.id.item_suggestion_layout)
        val name: TextView = itemView.findViewById(R.id.item_suggestion_name)
        val image: ImageView = itemView.findViewById(R.id.item_suggestion_image)
        val extras: TextView = itemView.findViewById(R.id.item_suggestion_extras)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchBoxViewHolder {
        val view = parent.inflate(R.layout.item_suggestion)
        return SearchBoxViewHolder(view)
    }

    override fun getItemCount(): Int = suggestions.size

    override fun onBindViewHolder(holder: SearchBoxViewHolder, position: Int) {
        val suggestion = suggestions[position]
        holder.name.text = suggestion.name
        if (suggestion.isPeak) {
            holder.image.setImageResource(R.drawable.peak_blue)
            val summits = context.resources.getQuantityString(R.plurals.summits, suggestion.summit_count, suggestion.summit_count)
            holder.extras.text = "${model.getElevationText(suggestion.elevation)} • ${suggestion.formattedLocation} • $summits"
            holder.extras.visibility = View.VISIBLE
        } else {
            holder.image.setImageResource(R.drawable.ic_location)
            holder.extras.visibility = View.GONE
        }
        holder.layout.setOnClickListener { observer.onNext(suggestion) }
    }
}