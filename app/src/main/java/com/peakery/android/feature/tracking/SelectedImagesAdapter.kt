package com.peakery.android.feature.tracking

import android.content.Context
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.peakery.android.R
import com.peakery.android.core.repository.SelectedImage
import com.peakery.android.inflate

class SelectedImagesAdapter(private val images: Collection<SelectedImage>, val context: Context): RecyclerView.Adapter<SelectedImagesAdapter.SelectedImageViewHolder>() {

    val deleteObserver = MutableLiveData<SelectedImage>()

    class SelectedImageViewHolder(itemView: View): RecyclerView.ViewHolder(itemView) {
        val image: ImageView = itemView.findViewById(R.id.item_selected_image)
        val buttonDelete: ImageView = itemView.findViewById(R.id.item_selected_image_delete)
        val loader: ViewGroup = itemView.findViewById(R.id.item_selected_image_loader)
    }

    override fun getItemCount(): Int = images.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SelectedImageViewHolder {
        return SelectedImageViewHolder(parent.inflate(R.layout.item_selected_image))
    }

    override fun onBindViewHolder(holder: SelectedImageViewHolder, position: Int) {
        val image = images.elementAt(position)
        Glide.with(context)
            .load(image.uri)
            .transition(DrawableTransitionOptions.withCrossFade())
            .centerCrop()
            .into(holder.image)
        holder.buttonDelete.setOnClickListener { deleteObserver.postValue(image) }
        holder.loader.visibility = if (image.isUploaded) View.GONE else View.VISIBLE
    }
}