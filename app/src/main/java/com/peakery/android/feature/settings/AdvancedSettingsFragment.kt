package com.peakery.android.feature.settings

import android.os.Bundle
import androidx.preference.Preference
import androidx.preference.PreferenceFragmentCompat
import com.peakery.android.R
import com.peakery.android.feature.history.HistoryActivity

class AdvancedSettingsFragment: PreferenceFragmentCompat() {

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        setPreferencesFromResource(R.xml.advanced_settings, rootKey)
    }

    override fun onPreferenceTreeClick(preference: Preference): <PERSON><PERSON>an {
        return when (preference.key) {
            "track_recovery" -> {
                HistoryActivity.start(activity)
                true
            }
            else -> { false } //do nothing for now
        }
    }
}