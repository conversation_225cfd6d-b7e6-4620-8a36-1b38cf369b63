package com.peakery.android.feature.peaks

import android.graphics.Typeface
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.Button
import android.widget.ProgressBar
import android.widget.TextView
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.transition.TransitionManager
import com.google.android.material.button.MaterialButtonToggleGroup
import com.peakery.android.*
import com.peakery.android.core.base.PeakeryFragment
import com.peakery.android.core.ui.HidingScrollListener
import com.peakery.android.feature.filters.Value
import com.peakery.android.feature.main.UiCoordinator
import com.peakery.android.feature.webview.WebViewActivity
import org.koin.android.ext.android.inject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.security.InvalidParameterException

class PeakListFragment: PeakeryFragment(R.layout.fragment_peak_list) {

    private val model: PeakListViewModel by viewModel()
    private val uiCoordinator: UiCoordinator by inject()

    private lateinit var peak_list: RecyclerView
    private lateinit var peak_sort: MaterialButtonToggleGroup
    private lateinit var peak_list_no_results: TextView
    private lateinit var peak_list_loader: ProgressBar
    private lateinit var peak_sort_layout: ViewGroup
    private lateinit var peak_sort_height: Button
    private lateinit var peak_sort_near: Button
    private lateinit var peak_list_header: ViewGroup
    private lateinit var peak_list_active_filters_padding: View


    companion object { fun new() : PeakListFragment = PeakListFragment() }

    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)

        initView()

        val adapter = PeakListAdapter(requireContext(), model)
        peak_list.adapter = adapter

        safeSubscribe(adapter.observer.subscribe { peak ->
            startActivity(WebViewActivity.newIntent(requireContext(), "${BuildConfig.web_url}/${peak.slug}"))
        })

        model.peakResults.observe(viewLifecycleOwner, Observer { peaks ->
            if (peaks.isEmpty()) {
                peak_list_no_results.visibility = View.VISIBLE
            } else {
                peak_list_no_results.visibility = View.GONE
            }

            adapter.update(peaks)
            peak_list.scrollToPosition(0)
        })
        model.peaksLoading.observe(viewLifecycleOwner, Observer { isLoading ->
            if (isLoading) {
                adapter.clear()
                peak_list_no_results.visibility = View.GONE
                peak_list_loader.visibility = View.VISIBLE
            } else {
                peak_list_loader.visibility = View.GONE
            }
        })
        uiCoordinator.activeFilters.observe(viewLifecycleOwner, Observer { visible ->
            TransitionManager.beginDelayedTransition(peak_list_header)
            peak_list_active_filters_padding.visibility = if (visible) View.VISIBLE else View.GONE
        })
        initSort()

        peak_list_header.setPadding(0, getStatusBarHeight(), 0, 0)
    }

    private fun initView() {
        peak_list = requireView().findViewById(R.id.peak_list)
        peak_sort = requireView().findViewById(R.id.peak_sort)
        peak_list_no_results = requireView().findViewById(R.id.peak_list_no_results)
        peak_list_loader = requireView().findViewById(R.id.peak_list_loader)
        peak_sort_layout = requireView().findViewById(R.id.peak_sort_layout)
        peak_sort_height = requireView().findViewById(R.id.peak_sort_height)
        peak_sort_near = requireView().findViewById(R.id.peak_sort_near)
        peak_list_header = requireView().findViewById(R.id.peak_list_header)
        peak_list_active_filters_padding = requireView().findViewById(R.id.peak_list_active_filters_padding)
    }

    private fun initSort() {
        // auto hide ui
        peak_sort.afterMeasured {
            val hideHeight = (peak_sort_layout.height * 2)
            peak_list.addOnScrollListener(object: HidingScrollListener(context, hideHeight) {
                override fun onMoved(distance: Int) {
                    peak_sort_layout.translationY = -distance.toFloat()
                }

                override fun onShow() {
                    peak_sort_layout.animate().translationY(0f).setInterpolator(DecelerateInterpolator(2f)).start()
                }

                override fun onHide() {
                    peak_sort_layout.animate().translationY(hideHeight.toFloat()).setInterpolator(AccelerateInterpolator(2f)).start()
                }

            })
        }

        // click listeners
        peak_sort.addOnButtonCheckedListener { _, checkedId, isChecked ->
            peak_sort.findViewById<Button>(checkedId).typeface = if (isChecked) Typeface.DEFAULT_BOLD else Typeface.DEFAULT
            if (isChecked) {
                model.selectSort(when (checkedId) {
                    R.id.peak_sort_popular -> Value.POPULAR
                    R.id.peak_sort_height -> Value.HIGHEST
                    R.id.peak_sort_near -> Value.NEARBY
                    else -> throw InvalidParameterException()
                })
            }
        }

        // default ui
        peak_sort_height.typeface = Typeface.DEFAULT
        peak_sort_near.typeface = Typeface.DEFAULT

        // observer
        model.sortFilter.observe(viewLifecycleOwner, Observer { filter ->
            when (filter?.value) {
                Value.POPULAR -> peak_sort.check(R.id.peak_sort_popular)
                Value.HIGHEST -> peak_sort.check(R.id.peak_sort_height)
                Value.NEARBY -> peak_sort.check(R.id.peak_sort_near)
                else -> {} // ignore
            }
        })
    }
}
