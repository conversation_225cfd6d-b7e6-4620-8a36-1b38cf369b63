package com.peakery.android.core.repository

import android.annotation.SuppressLint
import android.content.Context
import android.net.Uri
import com.peakery.android.core.network.S3Uploader
import com.peakery.android.core.network.UploadService
import io.reactivex.schedulers.Schedulers
import io.reactivex.subjects.BehaviorSubject
import okhttp3.MediaType
import okhttp3.RequestBody
import org.json.JSONObject
import java.io.File
import android.provider.MediaStore
import android.util.Log
import androidx.loader.content.CursorLoader
import com.peakery.android.core.network.S3_AUTH_TOKEN
import com.peakery.android.core.network.S3_BUCKET_NAME
import io.reactivex.Observable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import org.json.JSONArray
import java.util.*


const val REQUEST_CODE_IMAGE_SELECTION = 1447

class ImageSelectorRepository(private val uploadService: UploadService,
                              private val s3Uploader: S3Uploader,
                              private val userRepository: UserRepository,
                              private val context: Context) {

    private val images = mutableListOf<SelectedImage>()

    private var uploadCompletionObservable = BehaviorSubject.createDefault(ImageUploadStatus.NOT_STARTED)
    val selectedImagesObservable = BehaviorSubject.create<List<SelectedImage>>()

    fun getUploadCompletionObservable(): Observable<ImageUploadStatus> {
        startQueue()
        return uploadCompletionObservable
    }

    fun addSelectedImages(selectedImages: List<Uri>): BehaviorSubject<ImageUploadStatus> {
        for (uri in selectedImages) {
            val image = images.find { it.uri == uri }
            if (image == null) {
                val newImage = SelectedImage(null, uri, generateUuidFileName())
                this.images.add(newImage)
            }
        }
        selectedImagesObservable.onNext(this.images)
        startQueue()
        return uploadCompletionObservable
    }

    fun deleteImage(selectedImage: SelectedImage) {
        this.images.removeAll { it.uri == selectedImage.uri }
        selectedImagesObservable.onNext(this.images)
    }

    @SuppressLint("CheckResult")
    fun uploadImage(image: SelectedImage) {
        Log.e("uploadImage", "Start Image Upload")

        val signedUrlParams = JSONObject()
        signedUrlParams.put("auth_token", S3_AUTH_TOKEN)
        signedUrlParams.put("bucket_name", S3_BUCKET_NAME)
        signedUrlParams.put("content_type", "image/jpg")

        val file = File(getPath(image.uri))

        signedUrlParams.put("object_name", "items/users/${image.uuidFileName}")

        uploadService.getSignedUploadUrl(signedUrlParams.toString())
            .subscribeOn(Schedulers.io())
            .flatMap {
                if (it.isSuccessful) {
                    val imageBody = RequestBody.create("image/jpg".toMediaTypeOrNull(), file)
                    image.name = file.name
                    s3Uploader.upload(it.body()!!.data.url, imageBody)
                } else {
                    uploadCompletionObservable.onNext(ImageUploadStatus.FAILED)
                    throw Error("TODO: Handle error")

                }
            }
            .flatMap {
                val notifyPhotoUploadParams = JSONObject()
                notifyPhotoUploadParams.put("auth_token", userRepository.getUserToken())
                notifyPhotoUploadParams.put("photo", image.uuidFileName)
                uploadService.notifyPhotoUpload(notifyPhotoUploadParams.toString())
            }
            .subscribe ({
                image.isUploaded = true
                selectedImagesObservable.onNext(this.images)

                // Check that all images are uploaded
                if (areDownloadsCompleted()) {
                    uploadCompletionObservable.onNext(ImageUploadStatus.COMPLETED)
                }
            },
            { uploadCompletionObservable.onNext(ImageUploadStatus.FAILED) })
    }

    fun endSession() {
        uploadCompletionObservable.onNext(ImageUploadStatus.NOT_STARTED)
        images.clear()
    }

    private fun areDownloadsCompleted(): Boolean {
        return if (images.isEmpty()) { true }
               else { !images.any { !it.isUploaded } }
    }

    private fun startQueue() {
        // Only starts queue if image upload isn't in progress
        if (uploadCompletionObservable.value == ImageUploadStatus.IN_PROGRESS) {
            return
        }

        if (areDownloadsCompleted()) {
            uploadCompletionObservable.onNext(ImageUploadStatus.COMPLETED)
            return
        }

        uploadCompletionObservable.onNext(ImageUploadStatus.IN_PROGRESS)
        for (image in images) {
            uploadImage(image)
        }
    }

    private fun getPath(uri: Uri): String {
        val data = arrayOf(MediaStore.Images.Media.DATA)
        val loader = CursorLoader(context, uri, data, null, null, null)
        val cursor = loader.loadInBackground()!!
        val column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
        cursor.moveToFirst()
        return cursor.getString(column_index)
    }

    private fun generateUuidFileName(): String {
        return "${UUID.randomUUID()}.jpg"
    }

    fun getUploadedUuidJsonArray(): JSONArray {
        val array = JSONArray()
        images.forEach {
            val json = JSONObject()
            json.put("photo_filename", it.uuidFileName)
            array.put(json)
        }
        return array
    }
}

data class SelectedImage(
    var name: String?,
    val uri: Uri,
    val uuidFileName: String,
    var isUploaded: Boolean = false
)

enum class ImageUploadStatus {
    NOT_STARTED,
    IN_PROGRESS,
    COMPLETED,
    FAILED
}