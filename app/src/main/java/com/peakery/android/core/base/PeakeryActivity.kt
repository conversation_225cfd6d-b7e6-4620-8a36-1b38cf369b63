package com.peakery.android.core.base

import android.app.Dialog
import android.app.ProgressDialog
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.location.LocationManager
import android.os.Bundle
import android.provider.Settings
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.peakery.android.R
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable


abstract class PeakeryActivity: AppCompatActivity() {

    private var disposables: CompositeDisposable = CompositeDisposable()
    private lateinit var progressDialog: Dialog

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        checkForLocationEnabled()
    }

    private fun checkForLocationEnabled() {
        val lm = getSystemService(Context.LOCATION_SERVICE) as LocationManager
        var gpsEnabled = false

        try {
            gpsEnabled = lm.isProviderEnabled(LocationManager.GPS_PROVIDER)
        } catch(e: Exception) {}

        if(!gpsEnabled) {
            MaterialAlertDialogBuilder(this)
                .setTitle("Location is disabled")
                .setMessage("Enable Location in settings to continue")
                .setPositiveButton(R.string.open_location_settings) { dialog, _ ->
                    startActivity(Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS))
                }
                .show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        disposables.clear()
    }

    override fun onPause() {
        super.onPause()
        dismissProgressDialog()
    }

    /**
     * Observe and automatically release resource upon Activity's destruction
     */
    fun safeSubscribe(d: Disposable) {
        disposables.add(d)
    }

    fun replaceFragmentWithAnimation(id: Int, fragment: Fragment, tag: String) {
        // Only replace/animate if the fragment is different
        val oldFragment = supportFragmentManager.findFragmentByTag(tag)
        if (oldFragment != null && oldFragment::class == fragment::class) return

        val transaction = supportFragmentManager.beginTransaction()
        transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
        transaction.replace(id, fragment, tag)
        transaction.commit()
    }

    fun addFragment(id: Int, fragment: Fragment) {
        supportFragmentManager.beginTransaction().replace(id, fragment).commit()
    }

    fun addFragmentWithSlideAnimation(id: Int, fragment: Fragment, tag: String) {
        val transaction = supportFragmentManager.beginTransaction()
        transaction.setCustomAnimations(R.anim.slide_in_up, R.anim.slide_out_up)
        transaction.replace(id, fragment, tag)
        transaction.commit()
    }

    fun addFragmentWithFadeAnimation(id: Int, fragment: Fragment, tag: String) {
        val transaction = supportFragmentManager.beginTransaction()
        transaction.setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
        transaction.replace(id, fragment, tag)
        transaction.commit()
    }


    fun addFragment(id: Int, fragment: Fragment, tag: String) {
        supportFragmentManager.beginTransaction().replace(id, fragment, tag).commit()
    }

    fun hideFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction().hide(fragment).commit()
    }

    fun showFragment(fragment: Fragment) {
        supportFragmentManager.beginTransaction().show(fragment).commit()
    }

    fun removeFragment(tag: String) {
        val fragment = supportFragmentManager.findFragmentByTag(tag)
        removeFragment(fragment)
    }

    fun removeFragment(fragment: Fragment?) {
        if (fragment != null) {
            supportFragmentManager.beginTransaction().remove(fragment).commit()
        }
    }

    protected fun dismissProgressDialog() {
        try { progressDialog.dismiss() }
        catch (e: Exception) { }
    }

    protected fun showProgressDialog(stringId: Int) {
        showProgressDialog(getString(stringId))
    }

    protected fun showProgressDialog(progressText: String) {
        progressDialog = ProgressDialog.show(this, progressText, "")
    }

}