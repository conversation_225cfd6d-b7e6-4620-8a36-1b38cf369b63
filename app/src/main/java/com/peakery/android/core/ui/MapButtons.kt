package com.peakery.android.core.ui

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.LinearLayout
import com.peakery.android.R

class MapButtons
@JvmOverloads constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int = 0)
    : LinearLayout(context, attrs, defStyleAttr) {

    private var map_button_location: View

    init {
        inflate(context, R.layout.map_buttons, this)
        map_button_location = findViewById(R.id.map_button_location)
    }

    fun hideLocationButton() {
        map_button_location.visibility = View.VISIBLE
    }

    fun showLocationButton() {
        map_button_location.visibility = View.VISIBLE
    }
}
