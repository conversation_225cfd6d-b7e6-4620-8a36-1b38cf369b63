package com.peakery.android.core.db

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.Point
import com.peakery.android.core.model.Track

@Database(entities = [Peak::class, Point::class, Track::class], exportSchema = false, version = 7)
abstract class LocalDatabase: RoomDatabase() {

    companion object {
        fun build(context: Context) =
            Room.databaseBuilder(context, LocalDatabase::class.java, "db")
                .addMigrations(MIGRATION_5_6)
                .addMigrations(MIGRATION_6_7)
                .build()
    }

    abstract fun localDao(): LocalDao

    abstract fun peakDao(): PeakDao
}

private val MIGRATION_6_7 = object : Migration(6, 7) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("ALTER TABLE `Peak` ADD COLUMN `region` TEXT")
        database.execSQL("ALTER TABLE `Peak` ADD COLUMN `country` TEXT")
    }
}

private val MIGRATION_5_6 = object : Migration(5, 6) {
    override fun migrate(database: SupportSQLiteDatabase) {
        database.execSQL("CREATE TABLE `Peak` " +
                "(`id` TEXT NOT NULL, " +
                "`name` TEXT NOT NULL, " +
                "`slug` TEXT NOT NULL, " +
                "`peak_highlights` TEXT NOT NULL, " +
                "`thumbnail` TEXT NOT NULL, " +
                "`lat` REAL NOT NULL, " +
                "`lng` REAL NOT NULL, " +
                "`summit_count` INTEGER NOT NULL, " +
                "`elevation` REAL NOT NULL, " +
                "`prominence` REAL NOT NULL, " +
                "`your_summits` INTEGER NOT NULL, " +
                "`your_attempts` INTEGER NOT NULL, " +
                "`challenge_count` INTEGER NOT NULL, " +
                "`is_classic` INTEGER NOT NULL, " +
                "PRIMARY KEY(`id`))")
    }
}