package com.peakery.android.core.model

import android.os.Parcelable
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonObject
import com.mapbox.mapboxsdk.geometry.LatLng
import kotlinx.android.parcel.Parcelize
import java.lang.Exception
import java.lang.reflect.Type

@Entity @Parcelize
data class Peak(
    @PrimaryKey val id: String,
    @ColumnInfo(name = "name") val name: String,
    @ColumnInfo(name = "slug") val slug: String,
    @ColumnInfo(name = "peak_highlights") val peak_highlights: String,
    @ColumnInfo(name = "thumbnail") val thumbnail_url: String,
    @ColumnInfo(name = "lat") val lat: Double,
    @ColumnInfo(name = "lng") val lng: Double,
    @ColumnInfo(name = "summit_count") val summit_count: Int,
    @ColumnInfo(name = "elevation") val elevation: Double,
    @ColumnInfo(name = "prominence") val prominence: Double,
    @ColumnInfo(name = "your_summits") val your_summits: Int,
    @ColumnInfo(name = "your_attempts") val your_attempts: Int,
    @ColumnInfo(name = "challenge_count") val challenge_count: Int,
    @ColumnInfo(name = "is_classic") val is_classic: Boolean,
    @ColumnInfo(name = "region") var region: String?,
    @ColumnInfo(name = "country") var country: String?

): Parcelable {

    companion object {
        fun fromSuggestion(suggestion: Suggestion): Peak {
            return Peak(
                suggestion.id,
                suggestion.name,
                suggestion.url.replace("/", ""),
                "",
                suggestion.thumbnail_url,
                suggestion.lat,
                suggestion.lng,
                suggestion.summit_count,
                suggestion.elevation,
                0.0,
                0,
                0,
                0,
                false,
                suggestion.country?.firstOrNull()?.country_name,
                suggestion.region?.firstOrNull()?.region_name
            )
        }
    }
}

fun Peak.latLng(): LatLng = LatLng(lat, lng)

class PeakDeserializer: JsonDeserializer<Peak> {

    override fun deserialize(json: JsonElement, typeOfT: Type?, context: JsonDeserializationContext?): Peak {
        val obj = json.asJsonObject
        val regionObj = (obj.getAsJsonArray("region").firstOrNull() as JsonObject?)
        val countryObj = (obj.getAsJsonArray("country").firstOrNull() as JsonObject?)
        return Peak(
            obj.get("id").asString,
            obj.get("name").asString,
            obj.get("slug").asString,
            "",
            obj.get("thumbnail_url").asString,
            obj.get("lat").asDouble,
            obj.get("lng").asDouble,
            obj.get("summit_count").asInt,
            obj.get("elevation").asDouble,
            try { obj.get("prominence").asDouble } catch (e: Exception) { 0.0 },
            obj.get("your_summits").asInt,
            obj.get("your_attempts").asInt,
            obj.get("challenge_count").asInt,
            obj.get("is_classic").asBoolean,
            regionObj?.get("region_name")?.asString,
            countryObj?.get("country_name")?.asString
        )
    }
}