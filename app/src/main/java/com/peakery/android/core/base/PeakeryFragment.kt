package com.peakery.android.core.base

import android.app.Dialog
import android.app.ProgressDialog
import android.util.DisplayMetrics
import androidx.annotation.DimenRes
import androidx.annotation.IdRes
import androidx.annotation.LayoutRes
import androidx.fragment.app.Fragment
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable


abstract class PeakeryFragment(@LayoutRes contentLayoutId: Int): Fragment(contentLayoutId) {

    private var disposables: CompositeDisposable = CompositeDisposable()
    private lateinit var progressDialog: Dialog

    override fun onDetach() {
        super.onDetach()
        disposables.clear()
        dismissProgressDialog()
    }

    fun getSoftButtonsBarHeight(): Int {
        // getRealMetrics is only available with API 17 and +
        val metrics = DisplayMetrics()
        requireActivity().windowManager.defaultDisplay.getMetrics(metrics)
        val usableHeight = metrics.heightPixels
        requireActivity().windowManager.defaultDisplay.getRealMetrics(metrics)
        val realHeight = metrics.heightPixels
        return if (realHeight > usableHeight)
            realHeight - usableHeight
        else
            0
    }

    /**
     * Observe and automatically release resource upon Activity's destruction
     */
    fun safeSubscribe(d: Disposable) {
        disposables.add(d)
    }

    fun addFragment(id: Int, fragment: Fragment) {
        fragmentManager?.beginTransaction()?.replace(id, fragment)?.commit()
    }

    fun removeFragment(fragment: Fragment) {
        fragmentManager?.beginTransaction()?.remove(fragment)?.commit()
    }

    protected fun dismissProgressDialog() {
        try { progressDialog.dismiss() }
        catch (e: Exception) { }
    }

    protected fun showProgressDialog(stringId: Int) {
        showProgressDialog(getString(stringId))
    }

    protected fun showProgressDialog(progressText: String) {
        progressDialog = ProgressDialog.show(context, null, progressText)
    }
}
