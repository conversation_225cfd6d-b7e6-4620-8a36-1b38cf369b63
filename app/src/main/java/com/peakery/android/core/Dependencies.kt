package com.peakery.android.core

import android.content.Context
import android.preference.PreferenceManager
import com.google.android.gms.location.LocationServices
import com.google.android.libraries.places.api.Places
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.peakery.android.BuildConfig
import com.peakery.android.R
import com.peakery.android.core.location.CountryDetector
import com.peakery.android.core.db.LocalDatabase
import com.peakery.android.core.network.*
import com.peakery.android.core.repository.ImageSelectorRepository
import com.peakery.android.core.repository.PeakRepository
import com.peakery.android.core.repository.UserRepository
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.location.Tracker
import com.peakery.android.core.location.geotools.EarthGravitationalModel
import com.peakery.android.core.model.Peak
import com.peakery.android.core.model.PeakDeserializer
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.auth.AuthViewModel
import com.peakery.android.feature.filters.FiltersViewModel
import com.peakery.android.feature.history.HistoryDetailViewModel
import com.peakery.android.feature.history.HistoryViewModel
import com.peakery.android.feature.layers.LayerRepository
import com.peakery.android.feature.layers.LayersViewModel
import com.peakery.android.feature.main.MainViewModel
import com.peakery.android.feature.main.UiCoordinator
import com.peakery.android.feature.map.LocationDebugViewModel
import com.peakery.android.feature.map.MapChanges
import com.peakery.android.feature.map.MapViewModel
import com.peakery.android.feature.peaks.PeakListViewModel
import com.peakery.android.feature.searchbox.SearchBoxViewModel
import com.peakery.android.feature.settings.DeveloperSettingsViewModel
import com.peakery.android.feature.settings.SettingsViewModel
import com.peakery.android.feature.tabs.TabsViewModel
import com.peakery.android.feature.logging.LoggingViewModel
import com.peakery.android.feature.logging.finder.FinderViewModel
import com.peakery.android.feature.tracking.TrackingInfoViewModel
import com.peakery.android.feature.tracking.TrackingControlsViewModel
import com.peakery.android.feature.logging.map.PeakSelectionRepository
import com.peakery.android.feature.logging.map.PeakSelectionViewModel
import com.peakery.android.feature.webview.WebViewViewModel
import com.peakery.android.feature.webview.WebViewOverrideCoordinator
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.android.ext.koin.androidContext
import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit

val appModule = module {
    single { Tracker(get(), get(), get(), androidContext(), get(), get(), get(), get()) }
    single { StateMachine() }
    single { LocationServices.getFusedLocationProviderClient(androidContext()) }
    single { LocalDatabase.build(androidContext()).localDao() }
    single { LocalDatabase.build(androidContext()).peakDao() }
    single { LayerRepository(get()) }
    single { UiCoordinator() }
    single { WebViewOverrideCoordinator() }
    single { PreferenceManager.getDefaultSharedPreferences(androidContext()) }
    single { UserRepository(get()) }
    single { ImageSelectorRepository(get(), get(), get(), androidContext()) }
    single { CountryDetector(androidContext()) }
    single { UserPreferences(get(), get()) }
    single { AppUpdateManagerFactory.create(androidContext()) }

    single { createOkHttpClient() }
    single { createWebService<PeakService>(get()) }
    single { createWebService<UploadService>(get()) }
    single { createS3WebService<S3Uploader>() }
    single { PeakRepository(get(), get(), get(), get()) }
    single { MapChanges() }
    single { Analytics(get()) }
    single { FirebaseAnalytics.getInstance(androidContext()) }
    single { createEarthGravitationalModel(androidContext()) }
    single { Places.createClient(androidContext()) }

    viewModel { MainViewModel(get()) }
    viewModel { MapViewModel(get(), get(), get(), get(), get(), get(), get(), androidContext(), get()) }
    viewModel { SearchBoxViewModel(get(), get(), get()) }
    viewModel { WebViewViewModel(get(), get(), get()) }
    viewModel { TrackingControlsViewModel(get(), get(), get(), get(), get(), get(), get(), get()) }
    viewModel { TrackingInfoViewModel(get(), get()) }
    viewModel { HistoryViewModel(get()) }
    viewModel { HistoryDetailViewModel(get()) }
    viewModel { TabsViewModel(get(), get(), get(), get()) }
    viewModel { FiltersViewModel(get(), get()) }
    viewModel { LayersViewModel(get()) }
    viewModel { PeakListViewModel(get(), get()) }
    viewModel { AuthViewModel(get()) }
    viewModel { SettingsViewModel(get(), get(), get(), get()) }
    viewModel { DeveloperSettingsViewModel(get()) }
    viewModel { LocationDebugViewModel(get(), get()) }
    viewModel { FinderViewModel() }

    // logging
    viewModel { LoggingViewModel(get(), get(), get(), get(), get(), androidContext(), get(), get(), get(), get(), get()) }
    viewModel { PeakSelectionViewModel(get(), get(), get(), get(), get(), androidContext()) }
    single { PeakSelectionRepository() }
}

fun createEarthGravitationalModel(context: Context): EarthGravitationalModel {
    val egm = EarthGravitationalModel()
    val dataset = context.resources.openRawResource(R.raw.egm180)
    egm.load(dataset)
    return egm
}

inline fun <reified T> createWebService(okHttpClient: OkHttpClient): T {
    val retrofit = Retrofit.Builder()
        .baseUrl(BuildConfig.api_url)
        .client(okHttpClient)
        .addConverterFactory(ScalarsConverterFactory.create())
        .addConverterFactory(GsonConverterFactory.create(getGson()))
        .addCallAdapterFactory(RxJava2CallAdapterFactory.create()).build()
    return retrofit.create(T::class.java)
}

fun getGson(): Gson {
    return GsonBuilder().registerTypeAdapter(Peak::class.java, PeakDeserializer()).create()
}

private fun createOkHttpClient(): OkHttpClient {
    val httpLoggingInterceptor = HttpLoggingInterceptor()
    httpLoggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
    return OkHttpClient.Builder()
        .connectTimeout(40, TimeUnit.SECONDS)
        .readTimeout(40, TimeUnit.SECONDS)
        .addInterceptor(httpLoggingInterceptor)
        .addInterceptor(getHeaderInterceptor())
        .build()
}

inline fun <reified T> createS3WebService(): T {
    val httpLoggingInterceptor = HttpLoggingInterceptor()
    httpLoggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
    val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(100, TimeUnit.SECONDS)
        .readTimeout(100, TimeUnit.SECONDS)
        .writeTimeout(100, TimeUnit.SECONDS)
        .addInterceptor(httpLoggingInterceptor)
        .build()
    
    val retrofit = Retrofit.Builder()
        .baseUrl(BuildConfig.api_url)
        .client(okHttpClient)
        .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
        .build()
    return retrofit.create(T::class.java)
}

private fun getHeaderInterceptor(): Interceptor =
    Interceptor {
            chain ->
        val request = chain
            .request()
            .newBuilder()
            .addHeader("Accept", "application/json")
            .addHeader("Https", "on")
            .addHeader("Content-Type", "application/x-www-form-urlencoded; charset=utf-8")
            .addHeader("User-Agent", "PeakeryAndroid")
            .addHeader("apikey", API_KEY)

        chain.proceed(request.build())
    }