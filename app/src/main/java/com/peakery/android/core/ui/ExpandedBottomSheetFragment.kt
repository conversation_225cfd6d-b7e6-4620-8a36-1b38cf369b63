package com.peakery.android.core.ui

import android.app.Dialog
import android.os.Bundle
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import com.peakery.android.R
import com.google.android.material.bottomsheet.BottomSheetBehavior
import android.widget.FrameLayout
import android.view.WindowManager

open class ExpandedBottomSheetFragment: BottomSheetDialogFragment() {

    override fun getTheme(): Int = R.style.ExpandedBottomSheet

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog  {
        val bottomSheetDialog = BottomSheetDialog(requireContext(), theme)
        bottomSheetDialog.setOnShowListener {
            val bottomSheet = dialog!!.findViewById<FrameLayout>(com.google.android.material.R.id.design_bottom_sheet)
            BottomSheetBehavior.from(bottomSheet!!).state = BottomSheetBehavior.STATE_EXPANDED
            BottomSheetBehavior.from(bottomSheet).skipCollapsed = true
            BottomSheetBehavior.from(bottomSheet).isHideable = true
        }
        bottomSheetDialog.window?.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        return bottomSheetDialog
    }
}