package com.peakery.android.core.db

import androidx.room.*
import com.peakery.android.core.model.Point
import com.peakery.android.core.model.Track

@Dao
interface LocalDao {

    @Query("SELECT * FROM point")
    fun getLocations(): List<Point>

    @Query("SELECT * FROM point WHERE track_id LIKE :trackId")
    fun getLocations(trackId: Long): List<Point>

    @Query("SELECT * FROM track")
    fun getTracks(): List<Track>

    @Query("SELECT * FROM track WHERE id LIKE :id")
    fun getTrack(id: Long): Track

    @Query("SELECT * FROM track WHERE is_completed IS 0 LIMIT 1")
    fun getUncompletedTrack(): Track

    @Query("SELECT * FROM track WHERE is_completed IS 0")
    fun getUncompletedTracks(): List<Track>

    @Transaction
    fun archiveUncompletedTracks() {
        val uncompletedTracks = getUncompletedTracks()
        uncompletedTracks.forEach {
            it.isCompleted = true
            insertTrack(it)
        }
    }

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertTrack(track: Track): Long

    @Query("DELETE FROM track WHERE id IS :id")
    fun deleteTrack(id: Long)

    @Update
    fun updateTrack(track: Track)

    @Insert
    fun insertAll(vararg point: Point)
}