package com.peakery.android.core

import android.location.Location
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics

class Analytics(private val firebaseAnalytics: FirebaseAnalytics) {

    enum class Error(val value: String) {
        SUBMISSION("submission"),
        LOAD_PEAKS("load_peaks")
    }

    fun logError(error: Error, t: Throwable) {
        FirebaseCrashlytics.getInstance().recordException(t)

        val bundle = Bundle()
        bundle.putString("stacktrace", t.stackTrace.toString())
        firebaseAnalytics.logEvent(error.value, bundle)
    }

    fun logLocation(location: Location, valid: <PERSON>olean) {
        val bundle = Bundle()
        bundle.putString("point", location.toString())
        val suffix = if (valid) "valid" else "dropped"
        firebaseAnalytics.logEvent("debug_location_$suffix", bundle)
    }

    fun logBattery(batteryUsePerHour: Float) {
        val bundle = Bundle()
        bundle.putFloat("batteryUsePerHour", batteryUsePerHour)
        firebaseAnalytics.logEvent("battery", bundle)
    }

    fun logEvent(eventName: String) {
        firebaseAnalytics.logEvent(eventName, null)
    }
}