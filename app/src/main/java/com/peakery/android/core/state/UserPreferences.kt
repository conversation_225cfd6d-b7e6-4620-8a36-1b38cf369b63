package com.peakery.android.core.state

import android.content.SharedPreferences
import androidx.core.content.edit
import com.peakery.android.core.location.CountryDetector
import com.peakery.android.feetToMeters
import com.peakery.android.numberFormat
import io.reactivex.subjects.PublishSubject
import java.lang.IllegalStateException
import kotlin.math.roundToInt

private const val PREFERENCE_LENGTH_UNIT = "PREFERENCE_LENGTH_UNIT"
private const val PREFERENCE_OFFLINE_PEAKS = "PREFERENCE_OFFLINE_PEAKS"
private const val PREFERENCE_DEBUG_LOCATIONS = "PREFERENCE_DEBUG_LOCATIONS"
private const val PREFERENCE_GPS_INTERVAL = "PREFERENCE_GPS_INTERVAL"
private const val PREFERENCE_GPS_FAST_INTERVAL = "PREFERENCE_GPS_FAST_INTERVAL"
private const val PREFERENCE_GPS_TRACKING_OFF_INTERVAL = "PREFERENCE_GPS_TRACKING_OFF_INTERVAL"

class UserPreferences(private val sharedPreferences: SharedPreferences,
                      private val countryDetector: CountryDetector) {

    val lengthUnitChange = PublishSubject.create<LengthUnit>()

    fun getLengthUnit(): LengthUnit {
        val preferredLengthUnit = sharedPreferences.getString(PREFERENCE_LENGTH_UNIT, null)
        return if (preferredLengthUnit != null) {
            when (preferredLengthUnit) {
                LengthUnit.IMPERIAL.value -> LengthUnit.IMPERIAL
                LengthUnit.METRIC.value -> LengthUnit.METRIC
                else -> throw IllegalStateException("Invalid length unit")
            }
        } else {
            if (countryDetector.getDeviceCountryCode().isUnitedStates()) {
                LengthUnit.IMPERIAL
            } else {
                LengthUnit.METRIC
            }
        }
    }

    fun setUserLengthUnit(unit: LengthUnit) {
        sharedPreferences.edit().putString(PREFERENCE_LENGTH_UNIT, unit.value).apply()
        lengthUnitChange.onNext(unit)
    }

    fun getSmallUnitFromFeet(distance: Double): String {
        return when (getLengthUnit()) {
            LengthUnit.IMPERIAL -> "${distance.roundToInt().numberFormat()} ft"
            LengthUnit.METRIC -> "${distance.feetToMeters().numberFormat()} m"
        }
    }

    fun getSmallLengthUnit(): String {
        return when (getLengthUnit()) {
            LengthUnit.IMPERIAL -> "ft"
            LengthUnit.METRIC -> "m"
        }
    }

    fun getLargeLengthUnit(): String {
        return when (getLengthUnit()) {
            LengthUnit.IMPERIAL -> "miles"
            LengthUnit.METRIC -> "km"
        }
    }

    fun enableOfflinePeaks(enabled: Boolean) {
        sharedPreferences.edit {
            putBoolean(PREFERENCE_OFFLINE_PEAKS, enabled)
        }
    }

    fun enableDebugLocation(enabled: Boolean) {
        sharedPreferences.edit {
            putBoolean(PREFERENCE_DEBUG_LOCATIONS, enabled)
        }
    }

    fun isOfflinePeaksEnabled(): Boolean = sharedPreferences.getBoolean(PREFERENCE_OFFLINE_PEAKS, false)

    fun isDebugLocationsEnabled(): Boolean = sharedPreferences.getBoolean(PREFERENCE_DEBUG_LOCATIONS, false)

    fun isBetterLocationEnabled(): Boolean = true

    fun getGpsInterval() = sharedPreferences.getLong(PREFERENCE_GPS_INTERVAL, 7000)

    fun getFastGpsInterval() = sharedPreferences.getLong(PREFERENCE_GPS_FAST_INTERVAL, 5000)

    fun getTrackingOffGpsInterval() = sharedPreferences.getLong(PREFERENCE_GPS_TRACKING_OFF_INTERVAL, 10000)

    fun setGpsInterval(interval: Long) {
        sharedPreferences.edit().putLong(PREFERENCE_GPS_INTERVAL, interval).apply()
    }

    fun setFastGpsInterval(interval: Long) {
        sharedPreferences.edit {
            putLong(PREFERENCE_GPS_FAST_INTERVAL, interval)
        }
    }

    fun setTrackingOffInterval(interval: Long) {
        sharedPreferences.edit {
            putLong(PREFERENCE_GPS_TRACKING_OFF_INTERVAL, interval)
        }
    }
}

enum class LengthUnit(val value: String) {
    IMPERIAL("imperial"),
    METRIC("metric")
}