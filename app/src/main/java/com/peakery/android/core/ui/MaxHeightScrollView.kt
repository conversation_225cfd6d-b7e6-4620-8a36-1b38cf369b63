package com.peakery.android.core.ui

import android.content.Context
import android.util.AttributeSet
import androidx.core.widget.NestedScrollView

class MaxHeightScrollView
    @JvmOverloads constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int = 0): NestedScrollView(context, attrs, defStyleAttr) {

    private val NO_MAX_HEIGHT = -1
    private var maxHeight = NO_MAX_HEIGHT

    init {
        val a = context.theme.obtainStyledAttributes(
            attrs,
            com.peakery.android.R.styleable.ScrollViewWithMaxHeight,
            0, 0
        )

        try {
            maxHeight = a.getInt(com.peakery.android.R.styleable.ScrollViewWithMaxHeight_maxScrollViewHeight, 0)
        } finally {
            a.recycle()
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var newHeight = 0
        try {
            var heightSize = MeasureSpec.getSize(heightMeasureSpec)
            if (maxHeight != NO_MAX_HEIGHT && heightSize > maxHeight) {
                heightSize = maxHeight
            }
            newHeight = MeasureSpec.makeMeasureSpec(heightSize, MeasureSpec.AT_MOST)
            layoutParams.height = heightSize
        } catch (e: Exception) { } finally {
            super.onMeasure(widthMeasureSpec, newHeight)
        }

    }
}