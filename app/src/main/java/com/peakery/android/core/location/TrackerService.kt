package com.peakery.android.core.location

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.util.Log
import com.peakery.android.feature.main.MainActivity
import io.reactivex.disposables.CompositeDisposable
import org.koin.android.ext.android.inject
import android.app.PendingIntent
import android.os.*
import android.widget.RemoteViews
import com.peakery.android.*
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences
import java.math.BigDecimal
import java.math.RoundingMode
import kotlin.math.roundToInt

class TrackerService: Service() {

    private val notificationId = 424242

    private val tracker: Tracker by inject()
    private val userPreferences: UserPreferences by inject()

    private val mBinder = TrackerBinder()
    private var disposables: CompositeDisposable = CompositeDisposable()
    private var notificationDisposables: CompositeDisposable = CompositeDisposable()

    private val handler: Handler = Handler()
    private lateinit var powerManager: PowerManager
    private lateinit var wakeLock: PowerManager.WakeLock

    private var contentView: RemoteViews? = null
    private var notification: Notification? = null
    private var notificationManager: NotificationManager? = null

    private lateinit var wakeupIntent: PendingIntent
    private val heartbeat = object : Runnable {
        override fun run() {
            try {
                if (::wakeupIntent.isInitialized.not()) {
                    wakeupIntent = PendingIntent.getBroadcast(applicationContext, 0,
                        Intent("com.android.internal.location.ALARM_WAKEUP"), PendingIntent.FLAG_IMMUTABLE)
                }
                if (tracker.trackingState.value == Tracker.TrackingState.TRACKING_ON && powerManager.isDeviceIdleMode) {
                    try {
                        wakeupIntent.send()
                    } catch (e: Exception) {
                        Log.e("Heartbeat failed", e.toString())
                    }

                }
            } finally {
                handler.postDelayed(this, 10000)
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Log.e("TrackerService", "[START TRACKER SERVICE]")
        tracker.startPollingWithoutLocationTracking()
        disposables.add(tracker.trackingState.subscribe {
            if (it == Tracker.TrackingState.TRACKING_ON) {
                startForeground()
                keepServiceAlive()
            } else {
                stopForeground()
                stopServiceAlive()
            }
        })
    }

    private fun keepServiceAlive() {
        if (::powerManager.isInitialized.not()) {
            powerManager = getSystemService(POWER_SERVICE) as PowerManager
        }
        if (::wakeLock.isInitialized.not()) {
            wakeLock = powerManager.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "peakery:trackerservice")
        }
        handler.post(heartbeat)
        wakeLock.acquire()
    }

    private fun stopServiceAlive() {
        if (::wakeLock.isInitialized && wakeLock.isHeld) {
            wakeLock.release()
        }
        handler.removeCallbacksAndMessages(null)
    }

    private fun stopForeground() {
        stopForeground(true)
        contentView = null
        notificationDisposables.clear()
    }

    @SuppressLint("CheckResult")
    private fun startForeground() {
        startForeground(notificationId, getForegroundNotification())
        notificationDisposables.add(tracker.timerTicker.subscribe {
            if (contentView != null && notification != null && notificationManager != null) {
                contentView!!.setTextViewText(R.id.notification_time, it.elapsedTime.getFormattedTime())
                updateNotification(notification!!)
            }
        })
        notificationDisposables.add(tracker.position.subscribe {
            if (contentView != null && notification != null && notificationManager != null) {
                var altitudeInMeters = it.altitude
                if (altitudeInMeters < 0) altitudeInMeters = 0.0
                val computedAltitude = when (userPreferences.getLengthUnit()) {
                    LengthUnit.IMPERIAL -> altitudeInMeters.metersToFeet().numberFormat()
                    LengthUnit.METRIC -> altitudeInMeters.roundToInt().numberFormat()
                }

                contentView!!.setTextViewText(
                    R.id.notification_elevation,
                    "$computedAltitude ${userPreferences.getSmallLengthUnit()}"
                )
                updateNotification(notification!!)
            }
        })
        notificationDisposables.add(tracker.trackObservable.subscribe {
            if (contentView != null && notification != null && notificationManager != null) {
                val computedLength = when (userPreferences.getLengthUnit()) {
                    LengthUnit.IMPERIAL -> it.latLngs.computeLength().metersToMiles()
                    LengthUnit.METRIC -> it.latLngs.computeLength().metersToKm()
                }
                val twoDigitsDistance =
                    BigDecimal(computedLength).setScale(2, RoundingMode.HALF_EVEN)


                contentView!!.setTextViewText(
                    R.id.notification_distance,
                    "$twoDigitsDistance ${userPreferences.getLargeLengthUnit()}"
                )
                updateNotification(notification!!)
            }
        })
    }

    private fun getForegroundNotification(): Notification {
        val notificationBuilder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = this.javaClass.`package`?.name

            val channel = NotificationChannel(channelId, getString(R.string.app_name), NotificationManager.IMPORTANCE_LOW)
            channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE

            if (notificationManager == null) {
                notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            }
            notificationManager!!.createNotificationChannel(channel)
            Notification.Builder(this, channelId)
        } else {
            Notification.Builder(this)
                .setSound(null)
        }

        val notificationIntent = Intent(this, MainActivity::class.java)
        notificationIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        val pendingIntent = PendingIntent.getActivity(this, 0, notificationIntent, PendingIntent.FLAG_IMMUTABLE)

        notificationBuilder.setOngoing(true)
            .setContentTitle("Tracking in progress...")
            .setSmallIcon(R.drawable.ic_notification)
            .setColor(resources.getColor(R.color.notification))
            .setOnlyAlertOnce(true)
            .setContentIntent(pendingIntent)

        // content view
        contentView = RemoteViews(packageName, R.layout.notification_content)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            notificationBuilder
                .setCustomContentView(contentView)
                .setStyle(Notification.DecoratedCustomViewStyle())
        }

        notification = notificationBuilder.build()
        return notification!!
    }

    /**
     * NotificationManager can crash when too many updates are happening. We can ignore and
     * let the current update fail.
     */
    private fun updateNotification(notification: Notification) {
        try {
            notificationManager!!.notify(notificationId, notification)
        } catch (ignore: Exception) { }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        return START_STICKY
    }

    override fun onDestroy() {
        Log.e("TrackerService", "[STOP TRACKER SERVICE]")
        disposables.clear()
        tracker.onDestroy()
        super.onDestroy()
    }

    override fun onBind(intent: Intent): IBinder {
        stopForeground()
        return mBinder
    }
}

class TrackerBinder: Binder()
