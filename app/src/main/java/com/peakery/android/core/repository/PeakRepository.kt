package com.peakery.android.core.repository

import android.annotation.SuppressLint
import com.mapbox.mapboxsdk.geometry.LatLngBounds
import com.peakery.android.core.db.PeakDao
import com.peakery.android.core.model.*
import com.peakery.android.core.network.PeakService
import com.peakery.android.core.state.LengthUnit
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.feature.filters.*
import com.peakery.android.kmToMiles
import com.peakery.android.log
import com.peakery.android.metersToFeet
import io.reactivex.Maybe
import io.reactivex.Observable
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import io.reactivex.subjects.BehaviorSubject
import retrofit2.Response
import java.lang.NullPointerException

@SuppressLint("CheckResult")
class PeakRepository(private val peakService: PeakService,
                     private val userPreferences: UserPreferences,
                     private val userRepository: UserRepository,
                     private val peakDao: PeakDao
) {

    private var filters = getDefaultFilterList()

    var areFiltersConsumed: Boolean = true

    val peakResults = BehaviorSubject.create<Response<PeakResults>>()
    val peaksLoading = BehaviorSubject.create<Boolean>()
    val searchSelection = BehaviorSubject.create<Suggestion>()
    val filtersObservable = BehaviorSubject.createDefault(filters)

    init {
        userPreferences.lengthUnitChange.subscribe { resetFilters() }
    }

    fun getPeaksForBounds(latLngBounds: LatLngBounds) {
        this.areFiltersConsumed = true

        val params = getSearchParams()

        params["bounds"] = "${latLngBounds.southWest.latitude},${latLngBounds.southWest.longitude},${latLngBounds.northEast.latitude},${latLngBounds.northEast.longitude}"

        val token = userRepository.getUserToken()
        if (token != null) {
            params["auth_token"] = token
        }

        peaksLoading.onNext(true)
        peakService.getPeaksInBounds(params)
            .subscribeOn(Schedulers.io())
            .subscribe(
                {
                    if (it.isSuccessful) {
                        val peaks = it.body()!!.data.peaks
                        peakDao.insertPeaks(*peaks.toTypedArray())
                    }
                    peaksLoading.onNext(false)
                    peakResults.onNext(it)
                },
                {
                    it.log()
                    peaksLoading.onNext(false)
                }
            )
    }

    fun parseGpx(fileName: String): Observable<Response<PeakResults>> {
        val token = userRepository.getUserToken()?: return Observable.error(NullPointerException("Token is null (user logged out?)"))

        val params = HashMap<String, String>()
        params["auth_token"] = token
        params["gpx_filename"] = fileName

        return peakService.parseGpx(params)
    }

    /**
     * Only used in peak selection screen (manual logging)
     */
    fun getPeakSelectionForBounds(latLngBounds: LatLngBounds): Observable<Response<PeakResults>> {
        this.areFiltersConsumed = true

        val params = getSearchParams()

        params["bounds"] = "${latLngBounds.southWest.latitude},${latLngBounds.southWest.longitude},${latLngBounds.northEast.latitude},${latLngBounds.northEast.longitude}"

        val token = userRepository.getUserToken()
        if (token != null) {
            params["auth_token"] = token
        }

        peaksLoading.onNext(true)
        return peakService.getPeaksInBounds(params)
            .subscribeOn(Schedulers.io())
            .doOnNext {
                if (it.isSuccessful) {
                    val peaks = it.body()!!.data.peaks
                    peakDao.insertPeaks(*peaks.toTypedArray())
                }
            }
            .observeOn(AndroidSchedulers.mainThread())
    }

    fun getSuggestions(keywords: String): Observable<Response<SuggestionsResults>> {
        return peakService.getSuggestions(keywords)
    }

    fun getPeakSuggestions(keywords: String): Observable<Response<SuggestionsResults>> {
        return peakService.getSuggestionsByCategory(keywords, "peaks")
    }

    fun getPeak(peakId: String): Maybe<Peak> {
        return Maybe.fromCallable { peakDao.getPeak(peakId.toLong()) }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
    }

    fun getCachedPeaks(): Maybe<List<Peak>> {
        return Maybe.fromCallable { peakDao.getPeaks() }
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
    }

    private fun getSearchParams(): HashMap<String, String> {
        val params = HashMap<String, String>()
        val lengthUnit = userPreferences.getLengthUnit()

        handleDistanceParams(params, lengthUnit)
        handleVerticalGainParams(params, lengthUnit)
        handlePopularityParams(params)
        handleElevationParams(params, lengthUnit)
        handleProminenceParams(params, lengthUnit)
        handleDifficultyParams(params)
        handleLastClimbedParams(params)
        handleSortParams(params)

        return params
    }

    private fun handleDistanceParams(params: HashMap<String, String>, lengthUnit: LengthUnit) {
        val distanceFilter = filters.find { it.id == "distance" }!!
        var minDistance = distanceFilter.getMinValue()
        var maxDistance = distanceFilter.getMaxValue()

        // convert to miles if necessary
        if (lengthUnit == LengthUnit.METRIC) {
            minDistance = minDistance.kmToMiles()
            maxDistance = maxDistance.kmToMiles()
        }

        if (!distanceFilter.isMinDefault()) {
            params["route_distance_min"] = minDistance.toString()
        }
        if (!distanceFilter.isMaxDefault()) {
            params["route_distance_max"] = maxDistance.toString()
        }
    }

    private fun handleLastClimbedParams(params: java.util.HashMap<String, String>) {
        val lastClimbedFilter = filters.find { it.id == "last_climbed"}!!
        val selectedValue = lastClimbedFilter.values!![lastClimbedFilter.selectedPosition!!]
        if (selectedValue != 0) {
            params["last_climbed"] = selectedValue.toString()
        }
    }

    private fun handleDifficultyParams(params: java.util.HashMap<String, String>) {
        val difficultyFilter = filters.find { it.id == "difficulty" }!!

        if (!difficultyFilter.isMinDefault()) {
            params["difficulty_min"] = difficultyFilter.getMinValue().toString()
        }
        if (!difficultyFilter.isMaxDefault()) {
            params["difficulty_max"] = difficultyFilter.getMaxValue().toString()
        }
    }

    private fun handleProminenceParams(params: java.util.HashMap<String, String>, lengthUnit: LengthUnit) {
        val prominenceFilter = filters.find { it.id == "prominence" }!!
        var minProminence = prominenceFilter.getMinValue()
        var maxProminence= prominenceFilter.getMaxValue()
        // convert to feet if necessary
        if (lengthUnit == LengthUnit.METRIC) {
            minProminence = minProminence.metersToFeet()
            maxProminence = maxProminence.metersToFeet()
        }

        if (!prominenceFilter.isMinDefault()) {
            params["prom_min"] = minProminence.toString()
        }
        if (!prominenceFilter.isMaxDefault()) {
            params["prom_max"] = maxProminence.toString()
        }
    }

    private fun handleElevationParams(params: java.util.HashMap<String, String>, lengthUnit: LengthUnit) {
        val elevationFilter = filters.find { it.id == "elevation" }!!
        var minElevation= elevationFilter.getMinValue()
        var maxElevation= elevationFilter.getMaxValue()
        // convert to feet if necessary
        if (lengthUnit == LengthUnit.METRIC) {
            minElevation = minElevation.metersToFeet()
            maxElevation = maxElevation.metersToFeet()
        }

        if (!elevationFilter.isMinDefault()) {
            params["elev_min"] = minElevation.toString()
        }
        if (!elevationFilter.isMaxDefault()) {
            params["elev_max"] = maxElevation.toString()
        }
    }

    private fun handlePopularityParams(params: java.util.HashMap<String, String>) {
        val popularityFilter = filters.find { it.id == "popularity" }!!
        if (!popularityFilter.isMinDefault()) {
            params["summits_min"] = popularityFilter.getMinValue().toString()
        }
        if (!popularityFilter.isMaxDefault()) {
            params["summits_max"] = popularityFilter.getMaxValue().toString()
        }

    }

    private fun handleVerticalGainParams(params: HashMap<String, String>, lengthUnit: LengthUnit) {
        val verticalGainFilter = filters.find { it.id == "vertical_gain" }!!
        var minVerticalGain = verticalGainFilter.getMinValue()
        var maxVerticalGain = verticalGainFilter.getMaxValue()
        // convert to feet if necessary
        if (lengthUnit == LengthUnit.METRIC) {
            minVerticalGain = minVerticalGain.metersToFeet()
            maxVerticalGain = maxVerticalGain.metersToFeet()
        }

        if (!verticalGainFilter.isMinDefault()){
            params["vertical_gain_min"] = minVerticalGain.toString()
        }
        if (!verticalGainFilter.isMaxDefault()) {
            params["vertical_gain_max"] = maxVerticalGain.toString()
        }
    }

    private fun handleSortParams(params: HashMap<String, String>) {
        val sortFilter = filters.find { it.id == "sort" }!!
        params["sort"] = sortFilter.value!!.value
    }

    fun updateSearchSelection(selection: Suggestion) {
        searchSelection.onNext(selection)
    }

    fun updateFilters(newFilters: MutableList<Filter>) {
        if (filters != newFilters) {
            areFiltersConsumed = false
        }
        this.filters = newFilters
        filtersObservable.onNext(this.filters)
    }

    fun updateFilter(newFilter: Filter) {
        val newFilters = filters.toMutableList()
        newFilters[newFilters.indexOfFirst { it.id == newFilter.id }] = newFilter
        updateFilters(newFilters)
    }

    fun areFiltersModified(excludeSort: Boolean = false): Boolean {
        val defaultFilterList = getDefaultFilterList().toMutableList()
        if (excludeSort) { defaultFilterList.removeIf { it.id == "sort" } }

        val currentFilters = filters.toMutableList()
        if (excludeSort) { currentFilters.removeIf { it.id == "sort" } }

        return currentFilters != defaultFilterList
    }

    fun getModifiedFilters(excludeSort: Boolean = false): MutableList<Filter> {
        val modifiedFilters = mutableListOf<Filter>()
        val defaultFilterList = getDefaultFilterList().toMutableList()
        if (excludeSort) {
            defaultFilterList.removeIf { it.id == "sort" }
        }
        defaultFilterList.forEachIndexed { i, filter ->
            val activeFilter = filters[i]
            if (filters[i] != filter) {
                modifiedFilters.add(activeFilter)
            }
        }
        return modifiedFilters
    }

    fun resetFilters() {
        val newFilters = getDefaultFilterList()
        areFiltersConsumed = filters == newFilters
        filters = newFilters
        filtersObservable.onNext(filters)
    }

    private fun getDefaultFilterList(): List<Filter> {
        return when (userPreferences.getLengthUnit()) {
            LengthUnit.METRIC -> metricFilters
            LengthUnit.IMPERIAL -> imperialFilters
        }
    }

    fun wipe() {
        resetFilters()
    }
}