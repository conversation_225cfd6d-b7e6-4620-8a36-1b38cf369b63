package com.peakery.android.core.state

import android.os.Parcel
import android.os.Parcelable
import io.reactivex.subjects.BehaviorSubject

class StateMachine {

    val state = BehaviorSubject.createDefault(StateUpdate(State.EXPLORE_MAP, null))

    enum class State() : Parcelable {

        EXPLORE_MAP,
        EXPLORE_LIST,
        TRACKING,
        LATEST,
        PROFILE;

        constructor(parcel: Parcel) : this()

        override fun writeToParcel(parcel: Parcel, flags: Int) {
            parcel.writeInt(ordinal)
        }

        override fun describeContents(): Int {
            return 0
        }

        companion object CREATOR : Parcelable.Creator<State> {
            override fun createFromParcel(parcel: Parcel): State {
                return values()[parcel.readInt()]
            }

            override fun newArray(size: Int): Array<State?> {
                return arrayOf()
            }
        }
    }

    fun reset() {
        state.onNext(StateUpdate(State.EXPLORE_MAP, null))
    }

    fun getState(): State = state.value!!.state

    fun updateState(newState: State) {
        val updatedState = StateUpdate(newState, this.state.value!!.state)
        this.state.onNext(updatedState)
    }

    fun revertToPreviousState() {
        if (state.value!!.previousState != null) {
            updateState(state.value!!.previousState!!)
        } else {
            // reset to default
            updateState(State.EXPLORE_MAP)
        }
    }
}