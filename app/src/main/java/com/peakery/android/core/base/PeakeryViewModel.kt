package com.peakery.android.core.base

import androidx.lifecycle.ViewModel
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable

abstract class PeakeryViewModel: ViewModel() {

    private val disposables = CompositeDisposable()

    fun safeSubscribe(disposable: Disposable) {
        disposables.add(disposable)
    }

    override fun onCleared() {
        super.onCleared()
        disposables.clear()
    }
}