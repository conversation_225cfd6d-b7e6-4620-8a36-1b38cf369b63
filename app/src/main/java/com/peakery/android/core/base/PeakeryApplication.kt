package com.peakery.android.core.base

import android.app.Application
import android.os.StrictMode
import android.os.StrictMode.VmPolicy
import com.google.android.libraries.places.api.Places
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.mapbox.mapboxsdk.Mapbox
import com.peakery.android.BuildConfig
import com.peakery.android.R
import com.peakery.android.core.appModule
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin


class PeakeryApplication: Application() {
    
    override fun onCreate() {
        super.onCreate()

        FirebaseAnalytics.getInstance(this)
        FirebaseApp.initializeApp(this);

        Places.initialize(this, getString(R.string.google_key))

        Mapbox.getInstance(this, getString(R.string.access_token))

        startKoin {
            androidLogger()
            androidContext(this@PeakeryApplication)
            modules(appModule)
        }

        if (BuildConfig.DEBUG) {
//            StrictMode.setThreadPolicy(
//                StrictMode.ThreadPolicy.Builder()
//                    .detectDiskReads()
//                    .detectDiskWrites()
//                    .detectNetwork()
//                    .penaltyLog()
//                    .build()
//            )
//            StrictMode.setVmPolicy(
//                VmPolicy.Builder()
//                    .detectLeakedSqlLiteObjects()
//                    .detectLeakedClosableObjects()
//                    .penaltyLog()
//                    .penaltyDeath()
//                    .build()
//            )
        }
    }
}