package com.peakery.android.core.ui;

import android.content.Context;
import android.view.View;

import androidx.recyclerview.widget.RecyclerView;

/*
* https://github.com/mzgreen/HideOnScrollExample/
*/
public abstract class HidingScrollListener extends RecyclerView.OnScrollListener {

    private static final float HIDE_THRESHOLD = 10;
    private static final float SHOW_THRESHOLD = 70;

    private int mCollapsingViewOffset = 0;
    private boolean mControlsVisible = true;
    private int mCollapsingViewHeight;
    private int mTotalScrolledDistance;

    public HidingScrollListener(Context context, int hideHeight) {
        mCollapsingViewHeight = hideHeight;
    }

    @Override
    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
        super.onScrollStateChanged(recyclerView, newState);

        if(newState == RecyclerView.SCROLL_STATE_IDLE) {
            if(mTotalScrolledDistance < mCollapsingViewHeight) {
                setVisible();
            } else {
                if (mControlsVisible) {
                    if (mCollapsingViewOffset > HIDE_THRESHOLD) {
                        setInvisible();
                    } else {
                       setVisible();
                    }
                } else {
                    if ((mCollapsingViewHeight - mCollapsingViewOffset) > SHOW_THRESHOLD) {
                        setVisible();
                    } else {
                        setInvisible();
                    }
                }
            }
        }

    }

    @Override
    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);

        clipCollapsingViewOffset();
        onMoved(mCollapsingViewOffset);

        if((mCollapsingViewOffset < mCollapsingViewHeight && dy>0) || (mCollapsingViewOffset >0 && dy<0)) {
            mCollapsingViewOffset += dy;
        }
        if (mTotalScrolledDistance < 0) {
            mTotalScrolledDistance = 0;
        } else {
            mTotalScrolledDistance += dy;
        }
    }

    private void clipCollapsingViewOffset() {
        if(mCollapsingViewOffset > mCollapsingViewHeight) {
            mCollapsingViewOffset = mCollapsingViewHeight;
        } else if(mCollapsingViewOffset < 0) {
            mCollapsingViewOffset = 0;
        }
    }

    private void setVisible() {
        if(mCollapsingViewOffset > 0) {
            onShow();
            mCollapsingViewOffset = 0;
        }
        mControlsVisible = true;
    }

    private void setInvisible() {
        if(mCollapsingViewOffset < mCollapsingViewHeight) {
            onHide();
            mCollapsingViewOffset = mCollapsingViewHeight;
        }
        mControlsVisible = false;
    }

    public abstract void onMoved(int distance);
    public abstract void onShow();
    public abstract void onHide();
}