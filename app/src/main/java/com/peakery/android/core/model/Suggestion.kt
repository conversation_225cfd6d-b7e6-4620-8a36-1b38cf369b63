package com.peakery.android.core.model

import androidx.room.*
import com.mapbox.mapboxsdk.geometry.LatLng

data class Suggestion(
    val id: String,
    val item_type: String,
    val name: String,
    val url: String,
    val thumbnail_url: String,
    val lat: Double,
    val lng: Double,
    val summit_count: Int,
    val elevation: Double,
    val region: List<Region>?,
    val country: List<Country>?
)

@Entity
data class Region(
    @PrimaryKey val region_id: String,
    @ColumnInfo(name = "region_name") val region_name: String,
    @ColumnInfo(name = "country_name") val country_name: String
)

class RegionConverter {

    @TypeConverter
    fun fromRegion() {

    }
}

@Entity
data class Country(
    @PrimaryKey val country_id: String,
    @ColumnInfo(name = "country_name") val country_name: String
)

val Suggestion.latLng: LatLng get() = LatLng(lat, lng)
val Suggestion.isPeak: Boolean get() = item_type == "peak"
val Suggestion.isCity: Boolean get() = item_type == "city"
val Suggestion.isRegion: Boolean get() = item_type == "region"
val Suggestion.isCountry: Boolean get() = item_type == "country"
val Suggestion.formattedLocation: String get() {
    return if (!region.isNullOrEmpty()) {
        "${region!![0].region_name}, ${region[0].country_name}"
    } else {
        ""
    }
}