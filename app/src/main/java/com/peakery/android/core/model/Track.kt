package com.peakery.android.core.model

import android.location.Location
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.mapbox.mapboxsdk.geometry.LatLng

@Entity
data class Track(
    @PrimaryKey(autoGenerate = true) var id: Long = 0,
    @ColumnInfo(name = "name") var name: String = "",
    @ColumnInfo(name="elapsed") var elapsedTime: Long = 0L,
    @ColumnInfo(name = "is_completed") var isCompleted: Boolean = false,
    @Ignore var locations: ArrayList<Location> = arrayListOf(),
    @Ignore var latLngs: ArrayList<LatLng> = arrayListOf(),
    @Ignore var startBattery: Float? = null
) {

    fun isNotEmpty(): Boolean {
        return locations.isNotEmpty()
    }
}