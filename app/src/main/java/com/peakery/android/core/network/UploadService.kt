package com.peakery.android.core.network

import com.peakery.android.core.model.SignedUrlResults
import com.peakery.android.core.model.TrackUploadResults
import io.reactivex.Observable
import retrofit2.Response
import retrofit2.http.*

interface UploadService {

    @POST("upload/get_presigned_put_url/")
    fun getSignedUploadUrl(@Body signedUrlParams: String): Observable<Response<SignedUrlResults>>

    @POST("upload/photo/")
    fun notifyPhotoUpload(@Body params: String): Observable<Response<TrackUploadResults>>

    @POST("upload/track/")
    fun uploadTrack(@Body params: String): Observable<Response<TrackUploadResults>>

    @POST("upload/climb/")
    fun uploadClimb(@Body params: String): Observable<Response<TrackUploadResults>>
}