package com.peakery.android.core.ui

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.content.Context
import android.os.Handler
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.animation.doOnEnd
import com.mikhaellopez.circularprogressbar.CircularProgressBar
import com.peakery.android.R

private const val LONG_PRESS_DURATION = 40f

class LongPressButton
@JvmOverloads constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int = 0)
    : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var pulseAnimator: ObjectAnimator

    private var long_press_button: View
    private var long_press_button_pulse: View
    private var long_press_button_progress: CircularProgressBar

    private var longPressListener: () -> Unit = { }

    init {
        View.inflate(context, R.layout.long_press_button, this)

        val a = context.obtainStyledAttributes(attrs, R.styleable.LongPressButton)
        val title= a.getText(R.styleable.LongPressButton_title)
        val subtitle = a.getText(R.styleable.LongPressButton_subtitle)
        val pulse = a.getBoolean(R.styleable.LongPressButton_pulse, false)

        a.recycle()

        val long_press_button_title = findViewById<TextView>(R.id.long_press_button_title)
        val long_press_button_subtitle = findViewById<TextView>(R.id.long_press_button_subtitle)
        long_press_button_progress = findViewById(R.id.long_press_button_progress)
        long_press_button = findViewById(R.id.long_press_button)
        long_press_button_pulse = findViewById<Button>(R.id.long_press_button_pulse)

        long_press_button_title.text = title

        if (subtitle.isNullOrEmpty()) {
            long_press_button_subtitle.visibility = View.GONE
        } else {
            long_press_button_subtitle.text = subtitle
        }

        long_press_button_progress.progressMax = LONG_PRESS_DURATION

        long_press_button.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> startLongPress()
                MotionEvent.ACTION_UP,
                MotionEvent.ACTION_CANCEL -> endLongPress()
            }
            true
        }

        if (pulse) {
            resumePulseAnimation()
        }
    }

    private fun resumePulseAnimation() {

        pulseAnimator = ObjectAnimator.ofPropertyValuesHolder(
            long_press_button_pulse,
            PropertyValuesHolder.ofFloat("scaleX", 1.5f),
            PropertyValuesHolder.ofFloat("scaleY", 1.5f),
            PropertyValuesHolder.ofFloat("alpha", 0f)
        )
        pulseAnimator.duration = 1000

        pulseAnimator.doOnEnd {
            Handler().postDelayed({
                pulseAnimator.start()
            }, 1000)
        }

        pulseAnimator.start()
    }

    private fun stopPulseAnimation() {
        if (::pulseAnimator.isInitialized) {
            pulseAnimator.end()
        }
    }

    private fun startLongPress() {
        handler.removeCallbacks(longPressRunnable)
        handler.removeCallbacks(reverseProgressRunnable)

        long_press_button.isPressed = true
        handler.postDelayed(longPressRunnable, 1)
    }

    private fun endLongPress() {
        handler.removeCallbacks(longPressRunnable)
        handler.removeCallbacks(reverseProgressRunnable)


        long_press_button.isPressed = false
        handler.postDelayed(reverseProgressRunnable, 1)
    }

    private val longPressRunnable: Runnable = run {
        Runnable {
            long_press_button_progress.progress += 1

            if (long_press_button_progress.progress >= LONG_PRESS_DURATION) {
                handler?.removeCallbacks(longPressRunnable)
                longPressListener.invoke()
            } else {
                handler?.postDelayed(longPressRunnable, 1)
            }
        }
    }

    private val reverseProgressRunnable: Runnable = run {
        Runnable {
            if (long_press_button_progress.progress - 2 < 0) {
                long_press_button_progress.progress = 0f
            } else {
                long_press_button_progress.progress -= 2
            }

            if (long_press_button_progress.progress <= 0f) {
                handler?.removeCallbacks(reverseProgressRunnable)
            } else {
                handler?.postDelayed(reverseProgressRunnable, 1)
            }
        }
    }

    fun setOnLongPressListener(listener: () -> Unit) {
        longPressListener = listener
    }
}
