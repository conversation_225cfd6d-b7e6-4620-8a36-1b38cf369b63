package com.peakery.android.core.location

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import android.location.Criteria
import android.location.Location
import android.location.LocationListener
import android.location.LocationManager
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import com.google.android.gms.common.api.ResolvableApiException
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.location.LocationCallback
import com.google.android.gms.location.LocationRequest
import com.google.android.gms.location.LocationResult
import com.google.android.gms.location.LocationServices
import com.google.android.gms.location.LocationSettingsRequest
import com.mapbox.mapboxsdk.geometry.LatLng
import com.peakery.android.core.Analytics
import com.peakery.android.core.location.geotools.EarthGravitationalModel
import com.peakery.android.core.location.geotools.egm96.Geoid
import com.peakery.android.core.db.LocalDao
import com.peakery.android.core.model.Point
import com.peakery.android.core.model.Track
import com.peakery.android.core.state.StateMachine
import com.peakery.android.core.state.UserPreferences
import com.peakery.android.getBatteryLevel
import com.peakery.android.log
import com.peakery.android.toLatLng
import io.reactivex.Completable
import io.reactivex.Observable
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import io.reactivex.subjects.BehaviorSubject
import java.util.concurrent.TimeUnit

private const val TRACKING_STATE = "TRACKING_STATE"

class Tracker(private val locationProvider: FusedLocationProviderClient,
              private val dao: LocalDao,
              private val stateMachine: StateMachine,
              private val context: Context,
              private val sharedPreferences: SharedPreferences,
              private val analytics: Analytics,
              private val preferences: UserPreferences,
              private val egm: EarthGravitationalModel) {

    val trackingState = BehaviorSubject.createDefault(TrackingState.SELECTION)
    val gpsState = BehaviorSubject.createDefault(GpsState.ACQUIRING)
    val position = BehaviorSubject.create<Location>()
    val trackObservable = BehaviorSubject.create<Track>()
    val trackCompletedObservable = BehaviorSubject.create<Track>()
    val locationNotFoundObservable = BehaviorSubject.create<ResolvableApiException>()
    val timerTicker = BehaviorSubject.create<Track>()
    val debugLocation = BehaviorSubject.create<DebugLocation>()

    var currentTrack = newTrack()

    private val locationRequest = LocationRequest()
    private val disposables = CompositeDisposable()
    private lateinit var  handler: Handler
    private lateinit var timer: Disposable

    // better location manager
    private val isBetterLocationEnabled = preferences.isBetterLocationEnabled()
    private val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager

    enum class TrackingState {
        SELECTION,
        MANUAL_SELECTION,
        TRACKING_NOT_STARTED,
        TRACKING_ON,
        TRACKING_PAUSED,
        POST_TRACKING,
    }

    enum class GpsState {
        ACQUIRING,
        ACQUIRED,
        NOT_FOUND,
    }

    init {
        Geoid.init(context)
        restoreTrack()
        startGpsStateHandler()
    }

    private fun startGpsStateHandler() {
        handler = Handler(context.mainLooper)
        handler.postDelayed({
            if (position.value == null) {
                gpsState.onNext(GpsState.NOT_FOUND)
            }
        }, TimeUnit.SECONDS.toMillis(40))
    }

    /**
     * Core tracking functionalities
     */
    private fun newTrack(): Track =
        Track(name = "New Track", startBattery = context.getBatteryLevel())

    /**
     * Attempt to restore any unsubmitted/uncompleted track
     */
    @SuppressLint("CheckResult")
    private fun restoreTrack() {
        Single.fromCallable { dao.getUncompletedTrack() }
            .subscribeOn(Schedulers.io())
            .flatMap {
                Log.e("restoreTrack", "Uncompleted track found")
                currentTrack = it
                Single.fromCallable { dao.getLocations(it.id) }
                    .subscribeOn(Schedulers.io())
            }
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe( {
                currentTrack.latLngs = it.toLatLngs()
                restoreTrackState()
                trackObservable.onNext(currentTrack)
                timerTicker.onNext(currentTrack)
            },
            { Log.e("restoreTrack", "No uncompleted track found") })
    }

    private fun restoreTrackState() {
        when (sharedPreferences.getString(TRACKING_STATE, "TRACKING_NOT_STARTED")) {
            "TRACKING_NOT_STARTED" -> { deleteTrack() }
            "TRACKING_PAUSED", "TRACKING_ON" -> {
                trackingState.onNext(TrackingState.TRACKING_PAUSED)
                stateMachine.updateState(StateMachine.State.TRACKING)
                pauseTracking()
            }
            "POST_TRACKING" -> {
                trackingState.onNext(TrackingState.POST_TRACKING)
                stateMachine.updateState(StateMachine.State.TRACKING)
                finishTracking()
            }
        }
    }

    fun recordNew() {
        if (trackingState.value != TrackingState.TRACKING_NOT_STARTED) {
            trackingState.onNext(TrackingState.TRACKING_NOT_STARTED)
        }
    }

    fun logPast() {
        if (trackingState.value != TrackingState.MANUAL_SELECTION) {
            trackingState.onNext(TrackingState.MANUAL_SELECTION)
        }
    }

    @SuppressLint("CheckResult")
    fun startTracking() {
        if (trackingState.value != TrackingState.TRACKING_ON) {
            trackingState.onNext(TrackingState.TRACKING_ON)
            sharedPreferences.edit().putString(TRACKING_STATE, "TRACKING_ON").apply()
        }
        currentTrack = newTrack()
        Single.fromCallable { dao.insertTrack(currentTrack) }
            .subscribeOn(Schedulers.io())
            .subscribe( { currentTrack.id = it }, { it.log() } )
        startTimer()
        startPollingWithLocationTracking()
    }

    fun deleteTrack() {
        resetTrackingState()
        stateMachine.revertToPreviousState()
        Single.fromCallable { dao.deleteTrack(trackObservable.value?.id ?: 0) }
            .map { dao.archiveUncompletedTracks() }
            .subscribeOn(Schedulers.io())
            .subscribe()
    }

    fun pauseTracking() {
        if (trackingState.value != TrackingState.TRACKING_PAUSED) {
            trackingState.onNext(TrackingState.TRACKING_PAUSED)
            sharedPreferences.edit().putString(TRACKING_STATE, "TRACKING_PAUSED").apply()
        }
        saveTrack(false)
        stopTimer()
        startPollingWithoutLocationTracking()
    }

    /**
     * Finish track. This mostly reflects a UI state and the track
     * still needs to be saved using saveTrack() or saveTrack().
     */
    fun finishTracking() {
        if (currentTrack.latLngs.size >= 2) {
            if (trackingState.value != TrackingState.POST_TRACKING) {
                trackingState.onNext(TrackingState.POST_TRACKING)
                sharedPreferences.edit().putString(TRACKING_STATE, "POST_TRACKING").apply()
            }
            saveTrack(false)
            stopTimer()
            startPollingWithoutLocationTracking()
        } else {
            deleteTrack()
        }
    }

    fun resumeTracking() {
        if (trackingState.value != TrackingState.TRACKING_ON) {
            trackingState.onNext(TrackingState.TRACKING_ON)
            sharedPreferences.edit().putString(TRACKING_STATE, "TRACKING_ON").apply()
        }
        startPollingWithLocationTracking()
        startTimer()
    }

    fun saveTrack() {
        saveTrack(true)
    }

    private fun saveTrack(completed: Boolean) {
        if (currentTrack.isNotEmpty()) {
            currentTrack.isCompleted = completed
            Single.fromCallable { dao.insertTrack(currentTrack) }
                .retry(3)
                .subscribeOn(Schedulers.io())
                .subscribe()
            trackObservable.onNext(currentTrack)
            trackCompletedObservable.onNext(currentTrack)
        }

        if (completed) {
            stateMachine.revertToPreviousState()
            resetTrackingState()
        }
    }

    fun resetTrackingState() {
        if (trackingState.value != TrackingState.SELECTION) {
            trackingState.onNext(TrackingState.SELECTION)
            sharedPreferences.edit().putString(TRACKING_STATE, "TRACKING_NOT_STARTED").apply()
        }
        currentTrack = newTrack()
        trackObservable.onNext(currentTrack)
        timerTicker.onNext(currentTrack)
        stopTimer()
    }

    /**
     * Time ticker functionalities
     */
    private fun startTimer() {
        timer = Observable.interval(1, TimeUnit.SECONDS, Schedulers.io())
            .map { currentTrack.elapsedTime += 1 }
            .flatMapSingle {
                timerTicker.onNext(currentTrack)
                Single.fromCallable { dao.insertTrack(currentTrack) }
            }
            .subscribe({}, { it.log() })
    }

    private fun stopTimer() {
        if (::timer.isInitialized && !timer.isDisposed) {
            timer.dispose()
        }
    }

    /**
     * Other
     */
    @SuppressLint("MissingPermission")
    fun startPollingWithoutLocationTracking() {
        if (isBetterLocationEnabled) {
            locationManager.removeUpdates(betterLocationListener)
            val criteria = Criteria().apply {
                accuracy = Criteria.ACCURACY_FINE
                isAltitudeRequired = false
                isBearingRequired = false
                isCostAllowed = true
                powerRequirement = Criteria.POWER_MEDIUM
            }
            val provider = locationManager.getBestProvider(criteria, false)
            locationManager.requestLocationUpdates(provider!!, 0, 0.0f, betterLocationListener)
        } else {
            locationRequest.priority = LocationRequest.PRIORITY_BALANCED_POWER_ACCURACY
            locationRequest.interval = preferences.getTrackingOffGpsInterval()
            locationRequest.fastestInterval = preferences.getTrackingOffGpsInterval()
            requestLocationUpdates()
        }
    }

    @SuppressLint("MissingPermission")
    private fun startPollingWithLocationTracking() {
        if (isBetterLocationEnabled) {
            locationManager.removeUpdates(betterLocationListener)
            val criteria = Criteria().apply {
                accuracy = Criteria.ACCURACY_FINE
                verticalAccuracy = Criteria.ACCURACY_HIGH
                isAltitudeRequired = false
                isBearingRequired = false
                isCostAllowed = true
                powerRequirement = Criteria.POWER_HIGH
            }
            val provider = locationManager.getBestProvider(criteria, false)
            locationManager.requestLocationUpdates(provider!!, 0, 0.0f, betterLocationListener)
        } else {
            locationRequest.priority = LocationRequest.PRIORITY_HIGH_ACCURACY
            locationRequest.interval = preferences.getGpsInterval()
            locationRequest.fastestInterval = preferences.getFastGpsInterval()
            locationRequest.smallestDisplacement = 1f
            requestLocationUpdates()
        }
    }

    @SuppressLint("MissingPermission")
    private fun requestLocationUpdates() {
        locationProvider.removeLocationUpdates(locationCallback)
        locationProvider.requestLocationUpdates(locationRequest, locationCallback, Looper.myLooper()!!)

        // Create LocationSettingsRequest object using location request
        val builder = LocationSettingsRequest.Builder()
        builder.addLocationRequest(locationRequest)
        builder.setAlwaysShow(true)
        val locationSettingsRequest = builder.build()

        // Check whether location settings are satisfied
        // https://developers.google.com/android/reference/com/google/android/gms/location/SettingsClient
        val settingsClient = LocationServices.getSettingsClient(context)
        val task = settingsClient.checkLocationSettings(locationSettingsRequest)
        
        task.addOnFailureListener {
            it.log()
            if (it is ResolvableApiException) {
                locationNotFoundObservable.onNext(it)
            }
        }
    }

    fun lastKnownLocation(): Location? = position?.value

    private val locationCallback = object: LocationCallback() {
        override fun onLocationResult(location: LocationResult) {
            Log.w("Tracker", "New location: ${location.lastLocation}")

            setGpsAcquired()

            val lastLocation = location.lastLocation
            val filterReasons = filterLocation(lastLocation!!)

            debugLocation.onNext(DebugLocation(lastLocation, filterReasons, lastLocation.altitude))

            if (trackingState.value == TrackingState.TRACKING_ON) {
                if (filterReasons.isEmpty()) {
                    val point = location.toPoint(currentTrack)
                    analytics.logLocation(lastLocation, true)

                    if (currentTrack.id != 0.toLong()) {
                        persistPointInDb(point)
                    }

                    currentTrack.locations.add(lastLocation)
                    currentTrack.latLngs.add(lastLocation.toLatLng())
                    trackObservable.onNext(currentTrack)
                } else {
                    analytics.logLocation(location.lastLocation!!, false)
                }
            }
            position.onNext(location.lastLocation!!)

            super.onLocationResult(location)
        }
    }

    private val betterLocationListener = object: LocationListener {

        override fun onLocationChanged(location: Location) {
            Log.w("Tracker", "New better location: ${location}")

            setGpsAcquired()

            // altitude fix
            val wgs84Altitude = location.altitude
            val offset = Geoid.getOffset(com.peakery.android.core.location.geotools.Location(location.latitude, location.longitude))
            val offsetCorrection = 61 // magic number for now, seems to address a consistent ~300ft gap with real altitude
            location.altitude = wgs84Altitude + offset + offsetCorrection

            val filterReasons = filterLocation(location)

            debugLocation.onNext(DebugLocation(location, filterReasons, wgs84Altitude))

            if (trackingState.value == TrackingState.TRACKING_ON) {
                if (filterReasons.isEmpty()) {
                    val point = location.toPoint(currentTrack)

                    if (currentTrack.id != 0.toLong()) {
                        persistPointInDb(point)
                    }

                    currentTrack.locations.add(location)
                    currentTrack.latLngs.add(location.toLatLng())
                    trackObservable.onNext(currentTrack)
                }
            }
            position.onNext(location)
        }

        override fun onStatusChanged(provider: String?, status: Int, extras: Bundle?) {
            Log.e("tracker", "onStatusChanged: $status $provider")
        }

        override fun onProviderDisabled(provider: String) {}

        override fun onProviderEnabled(provider: String) {}
    }

    private fun setGpsAcquired() {
        if (gpsState.value != GpsState.ACQUIRED) {
            gpsState.onNext(GpsState.ACQUIRED)
        }
    }

    fun lastTrackPoint(): LatLng? {
        return if (currentTrack.isNotEmpty()) { currentTrack.latLngs.last() }
               else { null }
    }

    private fun persistPointInDb(point: Point) {
        disposables.add(
            Completable.fromAction { dao.insertAll(point) }
                .subscribeOn(Schedulers.io())
                .subscribe({}, { it.log() })
        )
    }

    private fun filterLocation(location: Location): MutableList<FilterReason> {
        val reasons = mutableListOf<FilterReason>()

        // accuracy filter
        if (location.accuracy > 25) { // 25m accuracy
            reasons.add(FilterReason.INACCURATE)
        }

        // location jumps filter
        val lastLocation = currentTrack.locations.lastOrNull()
        if (lastLocation != null) {
            val timeBetweenPoints = location.time - lastLocation.time // 15sec interval
            val distanceBetweenPoints = lastLocation.distanceTo(location) // 100m jump
            if (distanceBetweenPoints > 100 && timeBetweenPoints < TimeUnit.SECONDS.toMillis(15)) {
                reasons.add(FilterReason.LOCATION_JUMP)
            }
        }
        return reasons
    }

    fun onDestroy() {
        locationManager.removeUpdates(betterLocationListener)
    }
}

enum class FilterReason {
    INACCURATE,
    LOCATION_JUMP
}

data class DebugLocation(
    val location: Location,
    val filters: List<FilterReason>,
    val wgs84Elevation: Double
)

private fun LocationResult.toPoint(track: Track): Point {
    return Point(
        time = lastLocation!!.time,
        trackId = track.id,
        elevation = lastLocation!!.altitude,
        latitude = lastLocation!!.latitude,
        longitude = lastLocation!!.longitude,
        trackName = track.name
    )
}

private fun Location.toPoint(track: Track): Point {
    return Point(
        time = time,
        trackId = track.id,
        elevation = altitude,
        latitude = latitude,
        longitude = longitude,
        trackName = track.name
    )
}

private fun List<Point>.toLatLngs(): ArrayList<LatLng> {
    val locations = arrayListOf<LatLng>()
    forEach {
        locations.add(LatLng(it.latitude, it.longitude))
    }
    return locations
}
