package com.peakery.android.core.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.RelativeLayout

/**
 * A RelativeLayout that has equals width and height (square)
 */
class SquareRelativeLayout
    @JvmOverloads constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int = 0)
    : RelativeLayout(context, attrs, defStyleAttr) {

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, widthMeasureSpec)
    }
}