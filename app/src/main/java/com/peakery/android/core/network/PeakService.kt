package com.peakery.android.core.network

import com.peakery.android.core.model.PeakResults
import com.peakery.android.core.model.SuggestionsResults
import io.reactivex.Observable
import io.reactivex.Single
import retrofit2.Response
import retrofit2.http.*

interface PeakService {

    @GET("peaks/search")
    fun getPeaks(@QueryMap params: Map<String, String>): Single<Response<PeakResults>>

    @GET("peaks/map")
    fun getPeaksInBounds(@QueryMap params: Map<String, String>): Observable<Response<PeakResults>>

    @GET("search/suggestions")
    fun getSuggestions(@Query("keyword") keyword: String): Observable<Response<SuggestionsResults>>

    @GET("search/suggestions")
    fun getSuggestionsByCategory(@Query("keyword") keyword: String, @Query("category") category: String): Observable<Response<SuggestionsResults>>

    @POST("peaks/parse_gpx")
    fun parseGpx(@QueryMap params: Map<String, String>): Observable<Response<PeakResults>>
}