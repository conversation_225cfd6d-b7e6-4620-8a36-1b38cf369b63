package com.peakery.android.core.db

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.peakery.android.core.model.Peak

@Dao
interface PeakDao {

    @Query("SELECT * FROM peak")
    fun getPeaks(): List<Peak>

    @Query("SELECT * FROM peak WHERE id LIKE :id")
    fun getPeak(id: Long): Peak

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertPeak(peak: Peak): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertPeaks(vararg peak: Peak)
}