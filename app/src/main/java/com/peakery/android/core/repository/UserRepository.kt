package com.peakery.android.core.repository

import android.content.SharedPreferences
import io.reactivex.subjects.BehaviorSubject

private const val USER_ID = "id"
private const val USER_NAME = "username"
private const val USER_TOKEN = "token"

class UserRepository(private val sharedPreferences: SharedPreferences) {

    val userNameSet = BehaviorSubject.createDefault(getUserName())

    fun isAuthenticated(): <PERSON><PERSON>an {
        val userId = sharedPreferences.getInt(USER_ID, -1)
        val username = sharedPreferences.getString(USER_NAME, null)
        val token = sharedPreferences.getString(USER_TOKEN, null)

        return userId != -1 && username != null && token != null
    }

    fun wipe() = sharedPreferences.edit().clear().apply()

    fun setUserId(id: Int) = putInt(USER_ID, id)
    fun getUserId(): Int = getInt(USER_ID)

    fun setUserName(username: String) {
        putString(USER_NAME, username)
        userNameSet.onNext(username)
    }
    fun getUserName(): String = getString(USER_NAME)?: ""

    fun setUserToken(token: String) = putString(USER_TOKEN, token)
    fun getUserToken(): String? = getString(USER_TOKEN)

    private fun putString(key: String, string: String) = sharedPreferences.edit().putString(key, string).apply()

    private fun getString(key: String): String? = sharedPreferences.getString(key, null)

    private fun putInt(key: String, int: Int) = sharedPreferences.edit().putInt(key, int).apply()

    private fun getInt(key: String): Int = sharedPreferences.getInt(key, -1)
}