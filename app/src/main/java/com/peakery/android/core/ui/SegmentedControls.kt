package com.peakery.android.core.ui

import android.content.Context
import android.util.AttributeSet
import android.widget.Button
import android.widget.LinearLayout
import com.peakery.android.R
import io.reactivex.subjects.PublishSubject
import java.security.InvalidParameterException

class  SegmentedControls
    @JvmOverloads constructor(context: Context, attrs: AttributeSet, defStyleAttr: Int = 0)
    : LinearLayout(context, attrs, defStyleAttr) {

    lateinit var listener: Listener
    var observer =  PublishSubject.create<Segment>()

    private var leftButton: Button
    private var rightButton: Button

    init {
        inflate(context, R.layout.segmented_control, this)

        leftButton = findViewById(R.id.segmented_control_left)
        rightButton = findViewById(R.id.segmented_control_right)

        var a = context.obtainStyledAttributes(attrs, R.styleable.SegmentedControls)
        var textLeft = a.getString(R.styleable.SegmentedControls_textLeft)
        var textRight = a.getString(R.styleable.SegmentedControls_textRight)
        a.recycle()

        leftButton.text = textLeft
        rightButton.text = textRight

        leftButton.isSelected = true

        leftButton.setOnClickListener {
            if (leftButton.isSelected) return@setOnClickListener

            leftButton.isSelected = true
            rightButton.isSelected = false

            observer.onNext(Segment(leftButton.text.toString(), 0))
            if (::listener.isInitialized) {
                listener.onSegmentSelected(0)
            }
        }

        rightButton.setOnClickListener {
            if (rightButton.isSelected) return@setOnClickListener

            leftButton.isSelected = false
            rightButton.isSelected = true

            observer.onNext(Segment(rightButton.text.toString(), 1))
            if (::listener.isInitialized) {
                listener.onSegmentSelected(1)
            }
        }
    }

    fun selectSegment(name: String) {
        if (leftButton.text.toString().toUpperCase() == name!!.toUpperCase()) {
            leftButton.callOnClick()
        } else if (rightButton.text.toString().toUpperCase() == name!!.toUpperCase()) {
            rightButton.callOnClick()
        }
    }

    fun setLeftText(text: String) {
        leftButton.text = text
    }

    fun setRightText(text: String) {
        rightButton.text = text
    }

    interface Listener {
        /**
         * Triggers when
         */
        fun onSegmentSelected(segment: Int)
    }
}

data class Segment(
    val text: String,
    val position: Int
)