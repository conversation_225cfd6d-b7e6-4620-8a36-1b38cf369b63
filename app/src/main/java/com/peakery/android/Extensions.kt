package com.peakery.android

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.location.Location
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.util.DisplayMetrics
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.DimenRes
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.mapbox.mapboxsdk.geometry.LatLng
import com.peakery.android.core.RATIO_KM_TO_MILES
import com.peakery.android.core.RATIO_METERS_TO_MILES
import com.peakery.android.core.RATIO_METER_FEET
import java.text.NumberFormat
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.math.*


fun Context.toast(context: Context? = applicationContext, message: String, duration: Int = Toast.LENGTH_SHORT){
    Toast.makeText(context, message , duration).show()
}

fun Activity.snack(@StringRes resId: Int) {
    Snackbar.make(findViewById(android.R.id.content), resId, Snackbar.LENGTH_SHORT).show()
}

fun Fragment.toast(context: Context?, message: String, duration: Int = Toast.LENGTH_SHORT){
    Toast.makeText(context, message , duration).show()
}

fun Fragment.snack(@StringRes resId: Int) {
    Snackbar.make(view!!, resId, Snackbar.LENGTH_SHORT).show()
}

fun Fragment.snack(message: String) {
    Snackbar.make(view!!, message, Snackbar.LENGTH_SHORT).show()
}

fun Fragment.okSnack(message: String) {
    Snackbar.make(view!!, message, Snackbar.LENGTH_INDEFINITE).apply {
        setAction(R.string.ok) {this.dismiss() }
        view.findViewById<TextView>(com.google.android.material.R.id.snackbar_text).maxLines = 5
        show()
    }
}

fun ViewGroup.inflate(layoutRes: Int): View {
    return LayoutInflater.from(context).inflate(layoutRes, this, false)
}

fun View.setMargins(
    left: Int? = null,
    top: Int? = null,
    right: Int? = null,
    bottom: Int? = null
) {
    val lp = layoutParams as? ViewGroup.MarginLayoutParams
        ?: return

    lp.setMargins(
        left ?: lp.leftMargin,
        top ?: lp.topMargin,
        right ?: lp.rightMargin,
        bottom ?: lp.rightMargin
    )

    layoutParams = lp
}

fun View.absY(): Float {
    val location = IntArray(2)
    this.getLocationOnScreen(location)
    return location[1].toFloat()
}

fun Location.toLatLng(): LatLng = LatLng(latitude, longitude)


fun LatLng.isValid(): Boolean = (latitude != 0.0 && longitude != 0.0)


fun Double.metersToFeet(): Int = (this * RATIO_METER_FEET).roundToInt()
fun Int.metersToFeet(): Int = (this * RATIO_METER_FEET).roundToInt()

fun Double.feetToMeters(): Int = (this / RATIO_METER_FEET).roundToInt()

fun Double.metersToMiles(): Double = (this / RATIO_METERS_TO_MILES)

fun Int.kmToMiles(): Int = (this * RATIO_KM_TO_MILES).roundToInt()

fun Double.metersToKm(): Double = (this / 1000)

fun Int.numberFormat(): String = NumberFormat.getInstance().format(this)

fun Int.toYears(): Int = (this / 365)

fun Double.numberFormat(): String = NumberFormat.getInstance().format(this)

fun Activity.getSoftButtonsBarHeight(): Int {
    // getRealMetrics is only available with API 17 and +
    val metrics = DisplayMetrics()
    windowManager.defaultDisplay.getMetrics(metrics)
    val usableHeight = metrics.heightPixels
    windowManager.defaultDisplay.getRealMetrics(metrics)
    val realHeight = metrics.heightPixels
    return if (realHeight > usableHeight)
        realHeight - usableHeight
    else
        0
}

fun Long.getFormattedTime(): String = String.format("%02d:%02d:%02d",(this/3600), ((this% 3600)/60), (this % 60))

/**
 * Returns the length of the given path, in meters, on Earth.
 */
fun List<LatLng>.computeLength(): Double {
    if (this.size < 2) {
        return 0.0
    } else {
        var length = 0.0
        val prev = this[0]
        var prevLat = Math.toRadians(prev.latitude)
        var prevLng = Math.toRadians(prev.longitude)

        var lng: Double
        val var8 = this.iterator()
        while (var8.hasNext()) {
            val point = var8.next()
            val lat = Math.toRadians(point.latitude)
            lng = Math.toRadians(point.longitude)
            length += distanceRadians(prevLat, prevLng, lat, lng)
            prevLat = lat
            prevLng = lng
        }

        return length * 6371009.0
    }
}

private fun distanceRadians(lat1: Double, lng1: Double, lat2: Double, lng2: Double): Double {
    return arcHav(havDistance(lat1, lat2, lng1 - lng2))
}

private fun arcHav(x: Double): Double {
    return 2.0 * asin(sqrt(x))
}

private fun havDistance(lat1: Double, lat2: Double, dLng: Double): Double {
    return hav(lat1 - lat2) + hav(dLng) * cos(lat1) * cos(lat2)
}

private fun hav(x: Double): Double {
    val sinHalf = sin(x * 0.5)
    return sinHalf * sinHalf
}

/**
 * Extension method to remove the required boilerplate for running code after a view has been
 * inflated and measured.
 *
 * <AUTHOR> Leiva
 * @see <a href="https://antonioleiva.com/kotlin-ongloballayoutlistener/>Kotlin recipes: OnGlobalLayoutListener</a>
 */
inline fun <T : View> T.afterMeasured(crossinline f: T.() -> Unit) {
    viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
        override fun onGlobalLayout() {
            if (measuredWidth > 0 && measuredHeight > 0) {
                viewTreeObserver.removeOnGlobalLayoutListener(this)
                f()
            }
        }
    })
}

/**
 * Found here: https://gist.github.com/lupajz/43068881d949e207d5efe2c40bde1866
 */
fun <T> LiveData<T>.debounce(duration: Long = 1000L) = MediatorLiveData<T>().also { mld ->
    val source = this
    val handler = Handler(Looper.getMainLooper())

    val runnable = Runnable {
        mld.value = source.value
    }

    mld.addSource(source) {
        handler.removeCallbacks(runnable)
        handler.postDelayed(runnable, duration)
    }
}

fun Context.getStatusBarHeight(): Int {
    // status bar height
    var statusBarHeight = 0
    val resourceId = resources.getIdentifier("status_bar_height", "dimen", "android")
    if (resourceId > 0) {
        statusBarHeight = resources.getDimensionPixelSize(resourceId)
    }

    return statusBarHeight
}

fun Context.getStatusBarHeightPlus(@DimenRes res: Int): Int {
    return getStatusBarHeight() + resources.getDimension(res).toInt()
}

fun Context.getActionBarHeight(): Int {
    // action bar height
    var actionBarHeight = 0
    val styledAttributes = theme.obtainStyledAttributes(
        intArrayOf(android.R.attr.actionBarSize)
    )
    actionBarHeight = styledAttributes.getDimension(0, 0f).toInt()
    styledAttributes.recycle()

    return actionBarHeight
}

fun Fragment.getStatusBarHeightPlus(@DimenRes res: Int): Int {
    return context?.getStatusBarHeightPlus(res)?: 0
}

fun Fragment.getStatusBarHeight(): Int {
    return context?.getStatusBarHeight()?: 0
}

fun Context.getBatteryLevel(): Float {
    try {
        val filter = IntentFilter(Intent.ACTION_BATTERY_CHANGED)
        val batteryStatus: Intent = registerReceiver(null, filter) ?: return -1f

        val level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1)
        val scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1)
        return level * 100 / scale.toFloat()
    } catch (e: Error) {
        return 0f
    }
}

fun Long.getRelativeTimeInDays(): Long {
    return TimeUnit.MILLISECONDS.toDays(System.currentTimeMillis().minus(this))
}

/**
 * Print exception and logs to Crashlytics
 */
fun Throwable.log() {
    printStackTrace()
    FirebaseCrashlytics.getInstance().recordException(this)
}