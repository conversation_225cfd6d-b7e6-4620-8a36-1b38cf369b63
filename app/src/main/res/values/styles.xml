<resources>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="multiSliderStyle">@style/Widget.MultiSliderActive</item>
        <item name="android:actionBarStyle">@style/ActionBar</item>
        <item name="materialAlertDialogTheme">@style/AlertDialog</item>
    </style>

    <style name="AppTheme.Translucent">
        <item name="android:navigationBarColor">@color/white</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:navigationBarColor">@color/darkest_grey</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppTheme.LogForm">
        <item name="android:navigationBarColor">@color/white</item>
        <item name="android:statusBarColor">#F4F4F4</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
    </style>

    <style name="ActionBar" >
        <item name="android:textColorPrimary">@color/white</item>
        <item name="android:background">@color/darkest_grey</item>
    </style>

    <style name="Text">
        <item name="android:textColor">@color/almost_black</item>
    </style>

    <style name="Text.TrackerRow">
        <item name="android:textColor">@color/almost_black</item>
    </style>

    <style name="Text.TrackerRow.Header">
        <item name="android:textSize">12sp</item>
    </style>

    <style name="Text.TrackerRow.Content">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">17sp</item>
    </style>

    <style name="Text.Label">
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">10sp</item>
        <item name="android:drawablePadding">3dp</item>
        <item name="android:padding">@dimen/small_margin</item>
        <item name="android:textAllCaps">true</item>
    </style>

    <style name="Text.Label.Challenge">
        <item name="android:background">@drawable/bg_label_challenge</item>
        <item name="android:drawableLeft">@drawable/ic_challenge</item>
    </style>

    <style name="Text.Label.Classic">
        <item name="android:background">@drawable/bg_label_classic</item>
        <item name="android:drawableLeft">@drawable/ic_star_inset</item>
    </style>

    <style name="Text.Label.Summits">
        <item name="android:background">@drawable/bg_label_summits</item>
        <item name="android:drawableLeft">@drawable/ic_check</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="InputSearch">
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">44dp</item>
        <item name="android:inputType">textNoSuggestions</item>
        <item name="android:maxLines">1</item>
        <item name="android:paddingLeft">10dp</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="android:imeOptions">actionSearch</item>
    </style>

    <!-- Bottom Sheet-->
    <style name="BottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@drawable/bg_bottom_sheet</item>
    </style>

    <style name="BaseBottomSheetDialog" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:isScrollContainer">true</item>
        <item name="bottomSheetStyle">@style/BottomSheet</item>
    </style>

    <style name="ExpandedBottomSheet" parent="BaseBottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:navigationBarColor">@color/white</item>
    </style>

    <style name="MapPeakBottomSheetDialog" parent="@style/Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="BottomSheetTitle">
        <item name="android:gravity">center_horizontal</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/almost_black</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Multislider -->
    <style name="Widget.MultiSliderActive" parent="android:Widget">
        <item name="rangeColor">@color/filter_color_active</item>
        <item name="thumbColor">@color/filter_color_active</item>
        <item name="trackColor">@color/filter_color_default</item>
    </style>

    <!-- Tab Button -->
    <style name="TabButton">
        <item name="android:clickable">true</item>
        <item name="android:background">@null</item>
        <item name="android:tint">@drawable/selector_tab</item>
    </style>

    <!-- Tracker Buttons -->
    <style name="TrackerButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="shapeAppearanceOverlay">@style/TrackerButtonShapeAppearance</item>
        <item name="android:padding">0dp</item>
        <item name="iconGravity">textEnd</item>
    </style>

    <style name="TrackerButton.Primary">
        <item name="android:textColor">@color/white</item>
        <item name="android:backgroundTint">@color/selector_tracker_button</item>
    </style>

    <style name="TrackerButton.Secondary" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:backgroundTint">@color/white</item>
        <item name="strokeColor">@color/colorAccent</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="shapeAppearanceOverlay">@style/TrackerButtonShapeAppearance</item>
        <item name="android:padding">0dp</item>
        <item name="iconGravity">textEnd</item>
    </style>

    <style name="TrackerButton.Pulse">
        <item name="android:textColor">@color/white</item>
        <item name="android:backgroundTint">@color/dimBlue</item>
    </style>

    <style name="TrackerButton.Cancel">
        <item name="strokeColor">@color/tracker_cancel</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/tracker_cancel</item>
        <item name="android:backgroundTint">@color/white</item>
    </style>

    <style name="TrackerImageButton" parent="@style/Widget.MaterialComponents.Button.Icon">
        <item name="android:backgroundTint">@color/selector_tracker_button</item>
        <item name="android:layout_width">80dp</item>
        <item name="android:layout_height">80dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="strokeColor">@color/colorAccent</item>
        <item name="strokeWidth">2dp</item>
        <item name="shapeAppearanceOverlay">@style/TrackerButtonShapeAppearance</item>
        <item name="android:padding">0dp</item>
        <item name="iconPadding">0dp</item>
        <item name="iconGravity">textStart</item>
    </style>

    <style name="TrackerImageButton.Pause">
        <item name="android:backgroundTint">@color/colorAccent</item>
    </style>

    <style name="TrackerImageButton.Delete">
        <item name="android:backgroundTint">@color/white</item>
        <item name="strokeWidth">2dp</item>
        <item name="iconTint">@color/tracker_cancel</item>
        <item name="strokeColor">@color/tracker_cancel</item>
    </style>

    <style name="TrackerButtonShapeAppearance">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">100%</item>
    </style>

    <style name="LoginDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:navigationBarColor">@color/white</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AlertDialog" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButton.Neutral</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButton</item>
        <item name="buttonBarNeutralButtonStyle">@style/AlertDialogButton.Neutral</item>
    </style>

    <style name="AlertDialogNeutral" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButton.Neutral</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButton.Neutral</item>
        <item name="buttonBarNeutralButtonStyle">@style/AlertDialogButton.Neutral</item>
    </style>

    <style name="AlertDialogButton" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:colorAccent">@color/colorAccent</item>
    </style>

    <style name="AlertDialogButton.Neutral" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/darkest_grey</item>
        <item name="android:colorAccent">@color/darkest_grey</item>
    </style>

    <style name="PullTab">
        <item name="android:background">@drawable/pull_tab</item>
        <item name="android:layout_width">25dp</item>
        <item name="android:layout_height">4dp</item>
    </style>

    <style name="Button">
        <item name="android:minHeight">65dp</item>
        <item name="cornerRadius">@dimen/radius</item>
    </style>

    <style name="Button.Primary">
        <item name="android:backgroundTint">@color/colorAccent</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Button.Cancel">
        <item name="android:backgroundTint">#EDEDED</item>
        <item name="android:textColor">#AFAFAF</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Button.ListSort" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:backgroundTint">@color/selector_sort_background</item>
        <item name="android:textColor">@color/selector_sort_text</item>
        <item name="android:layout_height">55dp</item>
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:textSize">15sp</item>
        <item name="strokeColor">@color/selector_sort_outline</item>
        <item name="android:padding">0dp</item>
        <item name="shapeAppearanceOverlay">@style/ListSortAppearance</item>
        <item name="android:inputType">textCapWords</item>
        <item name="android:typeface">normal</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="ListSortAppearance">
        <item name="cornerSize">@dimen/radius</item>
    </style>

    <style name="PeakRow">
        <item name="shapeAppearanceOverlay">@style/PeakRowCard</item>
        <item name="android:elevation">20dp</item>
        <item name="cardElevation">20dp</item>
        <item name="cardPreventCornerOverlap">false</item>
    </style>

    <style name="PeakRowCard">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopRight">@dimen/radius</item>
        <item name="cornerSizeTopLeft">@dimen/radius</item>
        <item name="cornerSizeBottomRight">@dimen/radius</item>
        <item name="cornerSizeBottomLeft">@dimen/radius</item>
    </style>

    <style name="Text.Debug">
        <item name="android:textColor">#F58</item>
        <item name="android:textStyle">bold</item>
        <item name="android:shadowColor">#000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">1</item>
    </style>

    <style name="Text.Title">
        <item name="android:textColor">#333</item>
        <item name="android:textSize">@dimen/text_title_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Subtitle">
        <item name="android:textColor">#333</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Log">
        <item name="android:textColor">#333</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Gpxsubtitle">
        <item name="android:textColor">#333</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="DatePickerTheme" parent="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="colorPrimary">@color/colorAccent</item>
    </style>

    <style name="DialogNoBackground">
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:navigationBarColor">@color/darkest_grey</item>
    </style>

    <style name="LoadingText">
        <item name="android:textColor">@color/white</item>
        <item name="android:layout_margin">50dp</item>
        <item name="android:text">@string/loading_peaks_from_gpx</item>
        <item name="android:textStyle">bold</item>
        <item name="autoSizeTextType">uniform</item>
        <item name="autoSizePresetSizes">@array/autosize_text_sizes_large</item>
        <item name="android:lines">1</item>
        <item name="android:textSize">22sp</item>
        <item name="android:gravity">center</item>
    </style>
</resources>
