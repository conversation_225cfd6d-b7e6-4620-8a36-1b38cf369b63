<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
            android:id="@+id/long_press_button"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:elevation="2dp"
            android:layout_gravity="center"
            android:orientation="vertical"
            android:gravity="center"
            android:background="@drawable/bg_circle_blue">

        <TextView
                android:id="@+id/long_press_button_subtitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Hold to"
                android:textColor="@color/white"
                android:layout_marginTop="-10dp"
                android:textSize="11sp"/>

        <TextView
                android:id="@+id/long_press_button_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Finish"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textAllCaps="true"/>
    </LinearLayout>

    <Button
            android:id="@+id/long_press_button_pulse"
            android:layout_gravity="center"
            style="@style/TrackerButton.Pulse"/>

    <com.mikhaellopez.circularprogressbar.CircularProgressBar
            android:id="@+id/long_press_button_progress"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_gravity="center"
            app:cpb_background_progressbar_color="@color/white"
            app:cpb_progressbar_color="#CCCCCC"
            app:cpb_progress_direction="to_left"
            app:cpb_progressbar_width="20dp"/>
</FrameLayout>
