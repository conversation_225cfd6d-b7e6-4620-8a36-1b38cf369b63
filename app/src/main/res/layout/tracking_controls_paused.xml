<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:clipToPadding="false"
        android:paddingBottom="5dp">

    <Button
            android:id="@+id/tracking_controls_resume"
            android:text="@string/resume"
            android:elevation="2dp"
            android:layout_marginRight="@dimen/tracking_button_spacing"
            style="@style/TrackerButton.Secondary"/>

    <com.peakery.android.core.ui.LongPressButton
            android:id="@+id/tracking_controls_pause_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            app:title="@string/finish"
            app:subtitle="@string/hold_to"
            android:layout_marginLeft="@dimen/tracking_button_spacing" />
</LinearLayout>