<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools">

    <com.google.android.material.card.MaterialCardView
            android:id="@+id/item_selected_peak_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="2dp"
            android:layout_marginLeft="2dp"
            app:cardCornerRadius="@dimen/radius"
            app:cardElevation="2dp">

            <RelativeLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                <ImageView
                        android:id="@+id/item_selected_peak_image"
                        android:layout_width="90dp"
                        android:layout_height="110dp"
                        android:layout_margin="@dimen/base_margin"/>

                <LinearLayout
                        android:id="@+id/item_selected_peak_info_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/base_margin"
                        android:layout_toRightOf="@id/item_selected_peak_image">

                    <TextView
                            android:id="@+id/item_selected_peak_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            tools:text="North Bubble"
                            android:textStyle="bold"
                            android:textSize="18sp"
                            android:textColor="@color/black"/>

                    <TextView
                            android:id="@+id/item_selected_peak_location"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/small_margin"
                            tools:text="Maine, United States" />

                    <TextView
                            android:id="@+id/item_selected_peak_extra_info"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/small_margin"
                            tools:text="869ft - 35 climbs" />

                </LinearLayout>

                <RadioGroup
                        android:id="@+id/item_selected_peak_radio"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_below="@id/item_selected_peak_info_layout"
                        android:layout_toRightOf="@id/item_selected_peak_image"
                        android:orientation="horizontal"
                        android:checkedButton="@id/item_selected_peak_radio_summit">

                    <RadioButton
                            android:id="@+id/item_selected_peak_radio_summit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/summit"/>


                    <RadioButton
                            android:id="@+id/item_selected_peak_radio_attempt"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/attempt"/>
                </RadioGroup>
            </RelativeLayout>
    </com.google.android.material.card.MaterialCardView>

    <ImageView
            android:id="@+id/item_selected_peak_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_delete_photo"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:elevation="10dp"/>
</RelativeLayout>