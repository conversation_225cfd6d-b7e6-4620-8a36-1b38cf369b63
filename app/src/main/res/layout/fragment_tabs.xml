<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
          xmlns:android="http://schemas.android.com/apk/res/android"
          android:orientation="horizontal"
          android:layout_width="match_parent"
          android:layout_height="70dp"
          android:background="@color/white"
          android:paddingTop="@dimen/base_margin"
          android:paddingBottom="@dimen/base_margin"
          android:clickable="true"
          android:elevation="5dp"
          android:gravity="center_vertical">

    <ImageView
            android:id="@+id/tab_map"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            style="@style/TabButton"
            android:src="@drawable/ic_map"
            android:layout_weight="1"/>

    <ImageView
            android:id="@+id/tab_list"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            style="@style/TabButton"
            android:src="@drawable/ic_list"
            android:layout_weight="1"/>
    <ImageView
            android:id="@+id/tab_record"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height_big"
            style="@style/TabButton"
            android:src="@drawable/ic_record_tab"
            android:layout_weight="1"/>
    <ImageView
            android:id="@+id/tab_recent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            style="@style/TabButton"
            android:src="@drawable/ic_recent"
            android:layout_weight="1"/>
    <ImageView
            android:id="@+id/tab_profile"
            android:layout_width="match_parent"
            android:layout_height="@dimen/tab_height"
            style="@style/TabButton"
            android:src="@drawable/ic_user"
            android:layout_weight="1"/>
</LinearLayout>
