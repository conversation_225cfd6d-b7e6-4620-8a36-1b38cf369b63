<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:orientation="vertical">

        <View   style="@style/PullTab"/>

        <TextView
                android:text="@string/map_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                style="@style/BottomSheetTitle"/>
    </LinearLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/separator_grey"
            android:layout_marginTop="@dimen/base_margin"
            android:layout_marginBottom="@dimen/base_margin"/>

    <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/layers_grid"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="3"/>
</LinearLayout>