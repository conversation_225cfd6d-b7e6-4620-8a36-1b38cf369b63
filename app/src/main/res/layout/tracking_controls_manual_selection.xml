<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/small_margin"
        android:layout_marginRight="@dimen/base_margin"
        android:layout_marginLeft="@dimen/base_margin"
        android:clipChildren="false">

        <Button
                android:id="@+id/tracking_controls_gpx_file"
                style="@style/Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="@string/log_climb_from_gpx_file"
                android:layout_marginBottom="@dimen/small_margin"/>

        <Button
                android:id="@+id/tracking_controls_log_manually"
                style="@style/Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="Log climb manually"
                android:layout_marginBottom="@dimen/small_margin" />

        <Button
                android:id="@+id/tracking_controls_log_cancel"
                style="@style/Button.Cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="@string/cancel"/>
</LinearLayout>