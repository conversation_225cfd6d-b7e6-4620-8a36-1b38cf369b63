<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <com.peakery.android.core.ui.MaxHeightScrollView
            android:id="@+id/scrollview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:maxScrollViewHeight="800">

        <LinearLayout
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content">

            <com.google.android.material.button.MaterialButton
                    android:id="@+id/tracking_controls_button_add_photos"
                    android:layout_width="match_parent"
                    android:layout_height="75dp"
                    android:text="@string/add_photos"
                    android:layout_marginRight="@dimen/base_margin"
                    android:layout_marginLeft="@dimen/base_margin"
                    android:backgroundTint="@color/colorAccent"
                    android:textColor="@color/white"
                    android:textSize="18sp"
                    android:gravity="center"
                    app:iconPadding="@dimen/base_margin"
                    app:icon="@drawable/ic_add_photo"
                    app:cornerRadius="@dimen/radius"
                    app:iconGravity="textStart"
                    android:textAllCaps="false"
                    app:iconTint="@color/white"
                    android:elevation="0dp"
                    android:stateListAnimator="@null"
                    style="@style/Widget.MaterialComponents.Button"/>

            <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/tracking_controls_image_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    android:layout_marginLeft="@dimen/base_margin"
                    android:layout_marginRight="@dimen/small_margin"
                    app:spanCount="4"/>

            <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/base_margin"
                    android:layout_margin="@dimen/base_margin"
                    app:boxBackgroundMode="none"
                    app:hintEnabled="false"
                    app:errorIconDrawable="@null">
                <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/review_submit_input_body"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:minHeight="100dp"
                        android:hint="@string/what_happened_out_there_tell_your_story_here"
                        android:inputType="textCapSentences|textMultiLine"
                        android:background="@drawable/bg_input_track"
                        android:paddingLeft="7dp"
                        android:paddingRight="7dp"
                        android:paddingTop="12dp"
                        android:paddingBottom="12dp"
                        android:gravity="top"
                        android:textSize="14sp"/>
            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
    </com.peakery.android.core.ui.MaxHeightScrollView>

    <LinearLayout android:id="@+id/tracking_controls"
                  android:orientation="horizontal"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:gravity="center"
                  android:clipToPadding="false"
                  android:paddingBottom="15dp"
                  android:layout_marginTop="@dimen/small_margin"
                  android:elevation="2dp">

        <Button
                android:id="@+id/tracking_controls_post_tracking_delete"
                android:elevation="2dp"
                android:layout_marginRight="@dimen/tracking_button_spacing_and_padding"
                app:icon="@drawable/ic_trash"
                style="@style/TrackerImageButton.Delete"/>

        <Button
                android:id="@+id/tracking_controls_pause_stop"
                android:text="@string/save"
                android:elevation="2dp"
                android:layout_marginLeft="@dimen/tracking_button_spacing_and_padding"
                style="@style/TrackerButton.Primary"/>
    </LinearLayout>
</LinearLayout>