<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
              android:orientation="horizontal"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:paddingLeft="@dimen/margin_filters"
              android:paddingRight="@dimen/margin_filters"
              android:paddingTop="@dimen/base_margin"
              android:paddingBottom="@dimen/base_margin"
              android:gravity="center_vertical">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_weight="2"
            android:layout_marginLeft="@dimen/base_margin">

        <TextView
                android:id="@+id/filter_item_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/black"
                android:text="Title"
                android:layout_marginBottom="3dp"
                tools:ignore="HardcodedText"/>

        <TextView
                android:id="@+id/filter_item_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:textSize="13sp"
                android:textColor="@color/dark_grey"
                android:text="Title"
                android:layout_marginTop="3dp"
                tools:ignore="HardcodedText"/>
    </LinearLayout>

    <io.apptik.widget.MultiSlider
            android:id="@+id/filter_item_slider"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:thumbNumber="2"
            app:drawThumbsApart="true"
            android:layout_marginRight="@dimen/base_margin"
            android:layout_weight="1"
            android:paddingBottom="@dimen/base_margin"
            android:paddingTop="@dimen/base_margin"
            app:scaleMax="100000"/>
</LinearLayout>