<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="vertical"
        android:background="@color/darkest_grey">

    <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <TextView
            android:id="@+id/loading_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/LoadingText"/>

        <TextView
                android:id="@+id/loading_text_shadow"
                style="@style/LoadingText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:shadowColor="@color/white"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="5" />
    </FrameLayout>

    <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/loading_animation"
            android:layout_width="200dp"
            android:layout_height="150dp"
            android:layout_marginRight="@dimen/base_margin"
            android:layout_marginLeft="@dimen/base_margin"
            android:layout_centerHorizontal="true"
            app:lottie_rawRes="@raw/lottie_mountains"
            app:lottie_loop="true"
            app:lottie_autoPlay="true"/>
</LinearLayout>