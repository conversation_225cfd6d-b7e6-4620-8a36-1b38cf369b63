<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:mapbox="http://schemas.android.com/apk/res-auto"
        xmlns:app="http://schemas.android.com/">

    <RelativeLayout
            android:id="@+id/peak_selection_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/base_margin"
            android:background="#f6f6f6">

        <TextView
                android:id="@+id/peak_selection_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/select_peak_from_map"
                style="@style/Text.Subtitle"
                android:layout_marginBottom="@dimen/base_margin"/>

        <com.google.android.material.card.MaterialCardView
                android:id="@+id/peak_selection_search_layout"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_toLeftOf="@id/peak_selection_search_go"
                android:layout_below="@id/peak_selection_title"
                app:cardCornerRadius="@dimen/radius"
                android:layout_margin="2dp"
                app:cardUseCompatPadding="true">

            <RelativeLayout
                    android:id="@+id/search_box_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp">

                <EditText
                        android:id="@+id/peak_selection_search_input"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:hint="@string/jump_to_location"
                        android:background="@android:color/transparent"
                        android:layout_centerVertical="true"
                        style="@style/InputSearch"/>

                <ProgressBar
                        android:id="@+id/peak_selection_search_loader"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:indeterminate="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/base_margin"
                        android:layout_toLeftOf="@id/peak_selection_search_clear"
                        android:layout_alignWithParentIfMissing="true"/>

                <ImageButton
                        android:id="@+id/peak_selection_search_clear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_clear"
                        android:background="@null"
                        android:layout_centerVertical="true"
                        android:tint="@color/grey_hint"
                        android:layout_alignParentRight="true"
                        android:layout_marginRight="@dimen/base_margin"
                        android:visibility="gone"/>
            </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <com.google.android.material.button.MaterialButton
                android:id="@+id/peak_selection_search_go"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/go"
                android:backgroundTint="@color/colorAccent"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:gravity="center"
                app:cornerRadius="@dimen/radius"
                android:textAllCaps="false"
                android:elevation="0dp"
                android:stateListAnimator="@null"
                android:layout_below="@id/peak_selection_title"
                android:layout_alignParentRight="true"
                android:layout_marginLeft="@dimen/base_margin"
                android:insetBottom="0dp"
                android:insetTop="0dp"
                android:layout_alignTop="@id/peak_selection_search_layout"
                android:layout_alignBottom="@id/peak_selection_search_layout"
                style="@style/Widget.MaterialComponents.Button"/>
    </RelativeLayout>

    <com.mapbox.mapboxsdk.maps.MapView
            android:id="@+id/peak_selection_map"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/peak_selection_header"
            android:layout_above="@id/peak_selection_controls"
            mapbox:mapbox_cameraZoom="5"
            mapbox:mapbox_cameraTargetLat="36.7782"
            mapbox:mapbox_cameraTargetLng="-119.4179"/>

    <com.peakery.android.core.ui.MapButtons
            android:id="@+id/peak_selection_map_buttons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_marginRight="@dimen/small_margin"
            android:layout_marginBottom="@dimen/large_margin"
            android:layout_above="@id/peak_selection_controls"/>


    <FrameLayout
            android:id="@+id/peak_selection_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/peak_selection_controls"/>

    <LinearLayout
            android:id="@+id/peak_selection_controls"
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:clipToPadding="false"
            android:paddingBottom="15dp"
            android:paddingTop="10dp"
            android:layout_alignParentBottom="true"
            android:background="@color/white"
            android:elevation="10dp">

        <Button
                android:id="@+id/peak_selection_button_cancel"
                android:text="@string/close"
                android:elevation="2dp"
                style="@style/TrackerButton.Cancel"/>

        <Button
                android:id="@+id/peak_selection_button_save"
                android:text="@string/add_to_log"
                android:elevation="2dp"
                android:layout_marginLeft="50dp"
                android:visibility="gone"
                style="@style/TrackerButton.Primary"/>
    </LinearLayout>
</RelativeLayout>