<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <WebView
             android:id="@+id/auth_webview"
             android:layout_width="match_parent"
             android:layout_height="match_parent"/>

    <ProgressBar
            android:id="@+id/webview_loader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:layout_centerInParent="true"
            android:visibility="visible"/>

    <ImageButton
            android:id="@+id/webview_button_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_clear"
            android:layout_marginRight="9dp"
            android:layout_marginTop="40dp"
            android:padding="10dp"
            android:tint="@color/white"
            android:background="@null"
            android:layout_alignParentRight="true"
            android:contentDescription="@string/close" />

    <ImageButton
            android:id="@+id/webview_button_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_back"
            android:layout_marginLeft="9dp"
            android:layout_marginTop="@dimen/margin_top_webview_back"
            android:padding="10dp"
            android:tint="@color/white"
            android:background="@color/transparent"
            android:visibility="gone"/>
</RelativeLayout>
