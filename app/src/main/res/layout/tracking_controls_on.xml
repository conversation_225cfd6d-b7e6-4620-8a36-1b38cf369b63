<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/small_margin"
        android:clipChildren="false">

        <com.peakery.android.core.ui.LongPressButton
                android:id="@+id/tracking_controls_pause_stop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:title="@string/pause"
                app:subtitle="@string/hold_to"
                app:pulse="true"/>
</FrameLayout>