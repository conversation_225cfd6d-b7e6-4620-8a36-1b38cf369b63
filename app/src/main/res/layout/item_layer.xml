<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:layout_margin="@dimen/base_margin">

    <com.peakery.android.core.ui.SquareRelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
        <ImageView
                android:id="@+id/layer_item_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:foreground="?android:attr/selectableItemBackground"/>

        <View
                android:id="@+id/layer_item_image_selected"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/fg_layer_selected"
                android:visibility="gone"/>
    </com.peakery.android.core.ui.SquareRelativeLayout>

    <TextView
            android:id="@+id/layer_item_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:textStyle="bold"
            android:textColor="@drawable/selector_text_dark_grey"/>
</LinearLayout>