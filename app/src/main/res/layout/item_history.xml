<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="50dp"
              android:padding="@dimen/base_margin">

    <TextView
            android:id="@+id/history_item_title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:textSize="20sp"/>

    <ImageButton
            android:id="@+id/history_item_share"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:background="@null"
            android:src="@android:drawable/ic_menu_share"/>
</RelativeLayout>