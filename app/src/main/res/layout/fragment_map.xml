<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                xmlns:mapbox="http://schemas.android.com/apk/res-auto"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

    <com.mapbox.mapboxsdk.maps.MapView
        android:id="@+id/main_map"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        mapbox:mapbox_cameraZoom="5"
        mapbox:mapbox_cameraTargetLat="36.7782"
        mapbox:mapbox_cameraTargetLng="-119.4179"/>

    <FrameLayout
            android:id="@+id/map_fragment_debug_location"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="120dp"
            android:layout_marginLeft="@dimen/base_margin"/>

    <com.peakery.android.core.ui.MapButtons
            android:id="@+id/map_buttons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentRight="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="@dimen/small_margin"
            android:layout_marginBottom="@dimen/tab_height_and_padding"/>

    <FrameLayout
            android:id="@+id/map_peak_fragment_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"/>
</RelativeLayout>