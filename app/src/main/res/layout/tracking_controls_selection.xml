<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/small_margin"
        android:layout_marginRight="@dimen/base_margin"
        android:layout_marginLeft="@dimen/base_margin"
        android:clipChildren="false">

        <Button
                android:id="@+id/tracking_controls_record_new"
                style="@style/Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="@string/record_new_climb"
                app:icon="@drawable/ic_record"
                android:layout_marginBottom="@dimen/small_margin"
                app:iconGravity="textStart"/>

        <Button
                android:id="@+id/tracking_controls_log_past"
                style="@style/Button.Primary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="@string/log_a_past_climb"
                android:layout_marginBottom="@dimen/small_margin" />

        <Button
                android:id="@+id/tracking_controls_log_cancel"
                style="@style/Button.Cancel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/white"
                android:text="@string/cancel"/>
</LinearLayout>