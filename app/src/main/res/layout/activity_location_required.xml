<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        android:foreground="@null"
        android:gravity="center">

    <TextView
            android:text="@string/location_required"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="22sp"
            android:textStyle="bold"/>

    <TextView
            android:text="@string/peakery_requires_location_permission"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:layout_marginTop="40dp"
            android:layout_marginRight="20dp"
            android:layout_marginLeft="20dp"
            android:layout_marginBottom="40dp"
            android:gravity="center"/>

    <TextView
            android:id="@+id/location_required_settings_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/go_to_settings"
            android:textColor="@color/white"
            android:background="@drawable/bg_button_white_outline"
            android:textStyle="bold"
            android:textSize="16sp"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"/>
</LinearLayout>