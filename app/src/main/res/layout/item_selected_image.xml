<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="80dp">

    <ImageView
            android:id="@+id/item_selected_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:layout_alignParentBottom="true"
            android:layout_marginRight="10dp"
            android:layout_marginTop="10dp"
            android:background="#aaa"/>

    <FrameLayout
            android:id="@+id/item_selected_image_loader"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignLeft="@id/item_selected_image"
            android:layout_alignRight="@id/item_selected_image"
            android:layout_alignTop="@id/item_selected_image"
            android:layout_alignBottom="@id/item_selected_image"
            android:background="#5000">

        <androidx.core.widget.ContentLoadingProgressBar
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center"
                style="?android:attr/progressBarStyleLarge"/>
    </FrameLayout>

    <ImageView
            android:id="@+id/item_selected_image_delete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/ic_delete_photo"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"/>
</RelativeLayout>