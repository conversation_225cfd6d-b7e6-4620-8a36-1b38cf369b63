<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

    <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <LinearLayout
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

            <TextView
                    android:text="@string/time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Header"/>

            <TextView
                    android:id="@+id/notification_time"
                    android:text="@string/_00_00_00"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Content"/>
        </LinearLayout>

        <LinearLayout
                android:id="@+id/tracker_row_layout_distance"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

            <TextView
                    android:text="@string/distance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Header"/>

            <TextView
                    android:id="@+id/notification_distance"
                    android:text="0.00"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Content"/>
        </LinearLayout>

        <LinearLayout
                android:id="@+id/tracker_row_layout_elevation"
                android:layout_weight="1"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

            <TextView
                    android:text="@string/elevation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Header"/>

            <TextView
                    android:id="@+id/notification_elevation"
                    android:text="1080ft"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/not_white_nor_black"
                    style="@style/Text.TrackerRow.Content"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
