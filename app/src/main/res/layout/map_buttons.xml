<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        app:cardElevation="4dp"
        app:cardUseCompatPadding="true"
        app:cardCornerRadius="@dimen/radius">

    <LinearLayout
            android:orientation="vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingRight="3dp"
            android:paddingLeft="3dp">

        <ImageButton
                android:id="@+id/map_button_layers"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_layers"
                android:tint="@color/dark_grey"
                android:background="?android:attr/selectableItemBackground"/>

        <View
                android:layout_width="match_parent"
                android:layout_height="2px"
                android:layout_marginLeft="3dp"
                android:layout_marginRight="3dp"
                android:background="#cccc"/>

        <ImageButton
                android:id="@+id/map_button_location"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/ic_my_location"
                android:tint="@color/dark_grey"
                android:background="?android:attr/selectableItemBackground"/>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
