<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <Button
        android:id="@+id/segmented_control_left"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:text="Left"
        android:textSize="17sp"
        android:textColor="@color/sc_text"
        android:background="@drawable/button_sc_left"
        android:textAllCaps="false"/>

    <Button
        android:id="@+id/segmented_control_right"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:text="Right"
        android:textSize="17sp"
        android:textColor="@color/sc_text"
        android:background="@drawable/button_sc_right"
        android:textAllCaps="false"/>
</LinearLayout>