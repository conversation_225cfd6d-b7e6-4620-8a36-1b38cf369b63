<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".feature.main.MainActivity">

    <FrameLayout
            android:id="@+id/main_layout_top_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:elevation="2dp"/>

    <FrameLayout
            android:id="@+id/main_layout_map"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    <FrameLayout
            android:id="@+id/main_layout_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    <FrameLayout
            android:id="@+id/main_layout_latest"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    <FrameLayout
            android:id="@+id/main_layout_profile"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>

    <FrameLayout
            android:id="@+id/main_layout_bottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true">


        <FrameLayout
                android:id="@+id/main_layout_bottom_tabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:outlineProvider="paddedBounds"
                android:elevation="15dp"/>

        <FrameLayout
                android:id="@+id/main_layout_bottom_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:outlineProvider="paddedBounds"
                android:elevation="15dp"/>
    </FrameLayout>
</RelativeLayout>