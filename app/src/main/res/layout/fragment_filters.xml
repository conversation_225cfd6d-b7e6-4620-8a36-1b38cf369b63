<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent">

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <View   style="@style/PullTab"
                android:layout_centerHorizontal="true" />

        <TextView
                android:text="@string/filter_peaks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/small_margin"
                style="@style/BottomSheetTitle"/>


        <ImageView
                android:id="@+id/filters_reset"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/reset"
                android:layout_marginRight="8dp"
                android:padding="@dimen/base_margin"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:layout_alignParentRight="true"
                android:src="@drawable/ic_reset"/>
    </RelativeLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:background="@color/separator_grey"
            android:layout_marginTop="3dp"
            android:layout_marginBottom="@dimen/small_margin"/>

    <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/filters_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:orientation="vertical"/>
</LinearLayout>