<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              android:orientation="horizontal"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:id="@+id/item_suggestion_layout"
              android:foreground="?android:attr/selectableItemBackground"
              android:clickable="true"
              android:focusable="true"
              android:padding="15dp"
              android:gravity="center_vertical">

    <ImageView
            android:id="@+id/item_suggestion_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/base_margin"/>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <TextView
                    android:id="@+id/item_suggestion_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textColor="@color/black"/>


        <TextView
                android:id="@+id/item_suggestion_extras"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@color/grey_hint"/>
    </LinearLayout>
</LinearLayout>