<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:orientation="vertical">

    <LinearLayout
            android:id="@+id/finder_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingLeft="@dimen/base_margin"
            android:paddingRight="@dimen/base_margin">

        <ProgressBar
                android:id="@+id/finder_loader"
                android:layout_width="30dp"
                android:layout_height="30dp"/>

        <TextView
                android:id="@+id/finder_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Looking up .gpx files on your phone..."
                android:gravity="center"
                android:textSize="18sp"
                android:layout_marginLeft="12dp"/>
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/finder_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:orientation="vertical"
            android:layout_below="@id/finder_header"
            android:layoutAnimation="@anim/layout_animation_list" />
</RelativeLayout>