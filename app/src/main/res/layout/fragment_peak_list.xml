<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:id="@+id/peak_list_root"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:background="@color/list_background">

    <LinearLayout
            android:id="@+id/peak_list_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/list_background"
            android:elevation="6dp">

        <View
                android:id="@+id/peak_list_base_header_height"
                android:layout_width="match_parent"
                android:layout_height="65dp"/>

        <View
                android:id="@+id/peak_list_active_filters_padding"
                android:layout_width="match_parent"
                android:layout_height="@dimen/height_active_filters"/>
    </LinearLayout>

    <FrameLayout
            android:id="@+id/peak_sort_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/peak_list_header"
            android:background="@color/list_background"
            android:paddingRight="@dimen/base_margin"
            android:paddingTop="@dimen/base_margin"
            android:paddingLeft="@dimen/base_margin"
            android:paddingBottom="2dp"
            android:elevation="5dp">

        <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/peak_sort"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:singleSelection="true"
                app:selectionRequired="true">

            <Button
                    android:id="@+id/peak_sort_popular"
                    android:text="@string/popular"
                    style="@style/Button.ListSort"/>

            <Button
                    android:id="@+id/peak_sort_height"
                    android:text="@string/highest"
                    style="@style/Button.ListSort"/>

            <Button
                    android:id="@+id/peak_sort_near"
                    android:text="@string/nearest"
                    style="@style/Button.ListSort"/>
        </com.google.android.material.button.MaterialButtonToggleGroup>
    </FrameLayout>

    <FrameLayout
            android:id="@+id/peak_list_list_layout"
            android:layout_below="@+id/peak_list_header"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/peak_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:orientation="vertical"
                android:layoutAnimation="@anim/layout_animation_list"
                android:clipToPadding="false"
                android:paddingTop="60dp"
                android:paddingBottom="75dp"/>

        <ProgressBar
                android:id="@+id/peak_list_loader"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:layout_gravity="center"/>

        <TextView
                android:id="@+id/peak_list_no_results"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_peaks_here_adjust_map_or_search_a_place"
                android:textColor="@color/white"
                android:layout_margin="50dp"
                android:gravity="center"
                android:layout_gravity="center"
                android:visibility="gone"/>
    </FrameLayout>
</RelativeLayout>