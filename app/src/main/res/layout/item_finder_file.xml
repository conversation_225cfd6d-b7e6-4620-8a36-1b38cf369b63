<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/item_finder_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingLeft="@dimen/base_margin"
        android:paddingRight="@dimen/base_margin"
        android:paddingTop="@dimen/base_margin"
        android:background="?android:attr/selectableItemBackground">

    <ImageView
            android:id="@+id/item_finder_file_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:src="@drawable/ic_file"
            android:tint="@color/colorAccent"
            android:layout_marginRight="@dimen/large_margin"
            android:layout_centerVertical="true"/>

    <TextView
            android:id="@+id/item_finder_file_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/black"
            android:layout_toRightOf="@id/item_finder_file_icon"
            android:singleLine="false"
            android:maxLines="2"
            android:ellipsize="end"
            tools:text="file.gpx"/>

    <TextView
            android:id="@+id/item_finder_file_meta"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textColor="#555"
            android:layout_toRightOf="@id/item_finder_file_icon"
            android:layout_below="@id/item_finder_file_name"
            tools:text="file.gpx"/>

    <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_toRightOf="@id/item_finder_file_icon"
            android:layout_marginRight="@dimen/base_margin"
            android:background="#333"
            android:layout_below="@id/item_finder_file_meta"
            android:layout_marginTop="@dimen/base_margin"/>
</RelativeLayout>