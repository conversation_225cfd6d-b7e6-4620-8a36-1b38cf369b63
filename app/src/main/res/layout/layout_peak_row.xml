<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/map_peak_layout"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:clickable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:background="@drawable/bg_item_peak"
                android:focusable="true"
                android:elevation="5dp">

    <ImageView
            android:id="@+id/map_peak_image"
            android:layout_width="@dimen/peak_row_image_width"
            android:layout_height="120dp"/>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_toRightOf="@id/map_peak_image"
            android:gravity="center_vertical">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginLeft="8dp">

            <TextView
                    android:id="@+id/map_peak_label_classic"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="classic"
                    android:layout_marginRight="@dimen/small_margin"
                    style="@style/Text.Label.Classic"/>

            <TextView
                    android:id="@+id/map_peak_label_challenge"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="9 challenges"
                    android:layout_marginRight="@dimen/small_margin"
                    style="@style/Text.Label.Challenge"/>

            <TextView
                    android:id="@+id/map_peak_label_summits"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="3 x"
                    style="@style/Text.Label.Summits"/>
        </LinearLayout>

        <TextView
                android:id="@+id/map_peak_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="@dimen/small_margin"
                android:layout_marginBottom="@dimen/small_margin"
                android:layout_marginTop="@dimen/small_margin"
                android:textColor="@color/black"/>

        <TextView
                android:id="@+id/map_peak_summary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="@dimen/small_margin"
                android:text="- • -"/>

    </LinearLayout>
</RelativeLayout>