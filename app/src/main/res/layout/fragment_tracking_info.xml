<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/tracker_row_root"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/small_margin"
        android:layout_marginLeft="@dimen/small_margin"
        app:cardElevation="5dp"
        app:cardCornerRadius="@dimen/radius"
        app:cardUseCompatPadding="true">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <LinearLayout
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:background="@color/white">

            <LinearLayout
                    android:id="@+id/tracker_row_layout_time"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                <TextView
                        android:text="@string/time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Header"/>

                <TextView
                        android:id="@+id/tracker_row_time"
                        android:text="@string/_00_00_00"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Content"/>
            </LinearLayout>

            <View
                    android:layout_width="1px"
                    android:layout_height="match_parent"
                    android:background="@color/separator_grey"/>

            <LinearLayout
                    android:id="@+id/tracker_row_layout_distance"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                <TextView
                        android:text="@string/distance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Header"/>

                <TextView
                        android:id="@+id/tracker_row_distance"
                        android:text="0.00"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Content"/>
            </LinearLayout>

            <View
                    android:layout_width="1px"
                    android:layout_height="match_parent"
                    android:background="@color/separator_grey"/>

            <LinearLayout
                    android:id="@+id/tracker_row_layout_elevation"
                    android:layout_weight="1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center">

                <TextView
                        android:text="@string/elevation"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Header"/>

                <TextView
                        android:id="@+id/tracker_row_elevation"
                        android:text="1080ft"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        style="@style/Text.TrackerRow.Content"/>
            </LinearLayout>
        </LinearLayout>

        <FrameLayout
                android:id="@+id/tracker_row_pause_state"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/orange_pulse_start"
                android:visibility="gone">

            <TextView
                    android:id="@+id/tracker_row_pause_state_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/paused"
                    android:textColor="@color/white"
                    android:layout_gravity="center"
                    android:textSize="16sp"
                    android:textStyle="bold"/>
        </FrameLayout>

        <FrameLayout
                android:id="@+id/tracker_row_gps_state"
                android:layout_width="match_parent"
                android:layout_height="45dp"
                android:background="@color/orange_pulse_start"
                android:visibility="gone">

            <TextView
                    android:id="@+id/tracker_row_gps_state_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/acquiring_gps"
                    android:textColor="@color/white"
                    android:layout_gravity="center"
                    android:textSize="16sp"
                    android:textStyle="bold"/>
        </FrameLayout>
    </LinearLayout>
</com.google.android.material.card.MaterialCardView>
