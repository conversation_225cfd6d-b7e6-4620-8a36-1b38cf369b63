<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
    <LinearLayout
                  android:orientation="horizontal"
                  android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:id="@+id/item_suggestion_layout"
                  android:foreground="?android:attr/selectableItemBackground"
                  android:background="@color/white"
                  android:clickable="true"
                  android:focusable="true"
                  android:gravity="center_vertical">

        <ImageView
                android:id="@+id/item_suggestion_image"
                android:layout_width="100dp"
                android:layout_height="100dp"/>

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center_vertical"
                android:padding="15dp">

                <TextView
                        android:id="@+id/item_suggestion_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="16sp"
                        android:textColor="@color/black"/>

                <TextView
                        android:id="@+id/item_suggestion_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="@color/grey_hint"/>

                <TextView
                        android:id="@+id/item_suggestion_extras"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="@color/grey_hint"/>
        </LinearLayout>
    </LinearLayout>

    <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/separator_grey"/>
</LinearLayout>