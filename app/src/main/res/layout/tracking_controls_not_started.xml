<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:clipToPadding="false"
        android:paddingBottom="15dp"
        android:paddingTop="10dp">

    <Button
            android:id="@+id/tracking_controls_pause_stop"
            android:text="@string/cancel"
            android:elevation="2dp"
            android:layout_marginRight="@dimen/tracking_button_spacing_and_padding"
            style="@style/TrackerButton.Cancel"/>

    <Button
            android:id="@+id/tracking_controls_resume"
            android:text="@string/start"
            android:elevation="2dp"
            android:layout_marginLeft="@dimen/tracking_button_spacing_and_padding"
            style="@style/TrackerButton.Primary"/>
</LinearLayout>