<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:id="@+id/search_box_root"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:layout_marginRight="3dp"
              android:layout_marginLeft="3dp"
              android:orientation="vertical">

        <com.google.android.material.card.MaterialCardView
              android:id="@+id/search_box_card"
              android:orientation="vertical"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              app:cardElevation="5dp"
              app:cardCornerRadius="@dimen/radius"
              app:cardUseCompatPadding="true">

        <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            <RelativeLayout
                    android:id="@+id/search_box_layout"
                    android:layout_width="match_parent"
                    android:layout_height="50dp">

                <ImageView
                        android:id="@+id/search_box_icon"
                        android:layout_width="25dp"
                        android:layout_height="25dp"
                        android:layout_marginLeft="@dimen/base_margin"
                        android:layout_marginTop="@dimen/base_margin"
                        android:layout_marginBottom="@dimen/base_margin"
                        android:layout_marginRight="@dimen/small_margin"
                        android:src="@drawable/ic_search"
                        android:tint="@color/grey_hint"
                        android:layout_centerVertical="true"/>

                <EditText
                        android:id="@+id/search_box_input"
                        android:layout_width="match_parent"
                        android:layout_height="28dp"
                        android:layout_toRightOf="@+id/search_box_icon"
                        android:layout_toLeftOf="@id/search_box_separator"
                        android:hint="@string/peak_or_location"
                        android:background="@android:color/transparent"
                        android:layout_centerVertical="true"
                        style="@style/InputSearch"/>

                <ProgressBar
                        android:id="@+id/search_box_loader"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:indeterminate="true"
                        android:layout_centerVertical="true"
                        android:layout_marginRight="@dimen/base_margin"
                        android:layout_toLeftOf="@id/search_box_clear"
                        android:layout_alignWithParentIfMissing="true"/>

                <ImageButton
                        android:id="@+id/search_box_clear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_clear"
                        android:background="@null"
                        android:layout_centerVertical="true"
                        android:tint="@color/grey_hint"
                        android:layout_marginRight="@dimen/base_margin"
                        android:layout_toLeftOf="@id/search_box_separator"
                        android:visibility="gone"/>

                <View
                        android:id="@+id/search_box_separator"
                        android:layout_width="1px"
                        android:layout_height="30dp"
                        android:layout_centerVertical="true"
                        android:background="@color/grey_hint"
                        android:layout_toLeftOf="@id/search_box_button_filter"/>

                <ImageButton
                        android:id="@+id/search_box_button_filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentRight="true"
                        android:src="@drawable/ic_tune"
                        android:tint="@color/grey_hint"
                        android:layout_centerVertical="true"
                        android:padding="@dimen/base_margin"
                        android:background="?android:attr/selectableItemBackground"/>
            </RelativeLayout>

            <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/search_active_filters"
                    android:clickable="true"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/height_active_filters"
                    android:orientation="horizontal"
                    android:background="@drawable/bg_active_filters"
                    android:padding="@dimen/base_margin"
                    android:layout_below="@+id/search_box_layout"
                    android:layoutAnimation="@anim/layout_animation_list"
                    android:visibility="gone"/>
        </RelativeLayout>
        </com.google.android.material.card.MaterialCardView>

        <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/white">

            <ProgressBar
                    android:id="@+id/suggestions_loader"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:indeterminate="true"
                    android:layout_gravity="center_horizontal"
                    android:visibility="gone"/>

            <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/suggestions_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:orientation="vertical"
                    android:layoutAnimation="@anim/layout_animation_list"
                    android:visibility="gone"/>
        </FrameLayout>
</LinearLayout>
