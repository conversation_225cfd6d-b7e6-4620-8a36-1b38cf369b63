<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              xmlns:tools="http://schemas.android.com/tools"
              android:orientation="horizontal"
              android:layout_width="match_parent"
              android:layout_height="wrap_content"
              android:paddingLeft="@dimen/margin_filters"
              android:paddingRight="@dimen/margin_filters"
              android:paddingTop="@dimen/base_margin"
              android:paddingBottom="@dimen/base_margin"
              android:gravity="center_vertical">

    <TextView
            android:id="@+id/filter_item_select_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textSize="17sp"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:text="Title"
            android:layout_marginBottom="3dp"
            tools:ignore="HardcodedText"
            android:layout_weight="5"/>

    <com.peakery.android.core.ui.SegmentedControls
            android:id="@+id/filter_item_select_segments"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="12dp"
            android:paddingTop="12dp"
            android:layout_weight="2"/>
</LinearLayout>