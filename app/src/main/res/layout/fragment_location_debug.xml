<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <TextView
            android:id="@+id/debug_latitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="lat: 32.4230"
            style="@style/Text.Debug"/>

    <TextView
            android:id="@+id/debug_longitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="lon: 32.4230"
            style="@style/Text.Debug"/>

    <TextView
            android:id="@+id/debug_accuracy"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="acc: 28"
            style="@style/Text.Debug"/>

    <TextView
            android:id="@+id/debug_altitude"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="wgs84: 633"
            style="@style/Text.Debug"/>

    <TextView
            android:id="@+id/debug_validity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="VALID"
            android:textColor="#8BC34A"
            style="@style/Text.Debug"/>

    <TextView
            android:id="@+id/debug_reasons"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="INACCURATE"
            android:textColor="#F44336"
            style="@style/Text.Debug"/>
</LinearLayout>