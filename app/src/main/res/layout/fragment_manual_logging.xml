<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

    <include
            android:id="@+id/manual_logging_loading"
            layout="@layout/loading"
            android:visibility="gone"/>

    <RelativeLayout
            android:id="@+id/manual_logging_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#F4F4F4">

        <androidx.core.widget.NestedScrollView
                android:id="@+id/manual_logging_form"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@id/manual_logging_controls"
                android:paddingLeft="@dimen/base_margin"
                android:paddingRight="@dimen/base_margin"
                android:paddingTop="@dimen/small_margin">

            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                <TextView
                        android:id="@+id/manual_logging_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/log_your_climb"
                        style="@style/Text.Title"/>

                <TextView
                        android:id="@+id/manual_logging_gpx_file"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:singleLine="true"
                        android:ellipsize="middle"
                        tools:text="this is a test.gpx"
                        style="@style/Text.Gpxsubtitle"/>

                <TextView
                        android:id="@+id/manual_logging_peaks_you_climbed"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/peak_you_climbed"
                        style="@style/Text.Log"
                        android:layout_marginTop="20dp"
                        android:layout_marginBottom="@dimen/base_margin"/>

                <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/manual_logging_peak_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:visibility="gone"
                        android:layout_marginBottom="@dimen/base_margin"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        android:orientation="vertical"/>

                <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/manual_logging_button_add_peak_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:boxBackgroundMode="none"
                        app:hintEnabled="false"
                        app:errorIconDrawable="@null"
                        app:endIconDrawable="@drawable/ic_map"
                        app:endIconTint="#999"
                        app:endIconMode="custom"
                        android:layout_marginBottom="@dimen/extra_large_margin"
                        android:layout_below="@id/manual_logging_date_title">

                    <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/manual_logging_button_add_peak"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/search_peaks_"
                            android:inputType="text"
                            android:imeOptions="actionDone"
                            android:background="@drawable/bg_input_track"
                            android:paddingTop="15dp"
                            android:paddingBottom="15dp"
                            android:gravity="top"
                            android:textSize="16sp"/>
                </com.google.android.material.textfield.TextInputLayout>

                <TextView
                        android:id="@+id/manual_logging_button_add_more_peaks"
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_add_peak"
                        android:text="@string/add_another_peak_to_log"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:layout_marginBottom="@dimen/large_margin"/>

                <RelativeLayout
                        android:id="@+id/manual_logging_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/large_margin">

                    <TextView
                            android:id="@+id/manual_logging_date_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="@string/date_of_climb"
                            style="@style/Text.Log"
                            android:layout_toLeftOf="@id/manual_logging_date_hint"/>

                    <TextView
                            android:id="@+id/manual_logging_date_hint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/estimate_if_don_t_know_exact_date"
                            android:layout_alignParentRight="true"
                            android:textSize="10sp"
                            android:layout_marginTop="3dp"/>

                    <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/base_margin"
                            app:boxBackgroundMode="none"
                            app:hintEnabled="false"
                            app:errorIconDrawable="@null"
                            android:layout_below="@id/manual_logging_date_title">

                        <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/manual_logging_date_input"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:hint="@string/choose_date"
                                android:inputType="date"
                                android:focusable="false"
                                android:clickable="true"
                                android:editable="false"
                                android:cursorVisible="false"
                                android:focusableInTouchMode="false"
                                android:background="@drawable/bg_input_track"
                                android:paddingTop="15dp"
                                android:paddingBottom="15dp"
                                android:gravity="top"
                                android:textSize="16sp"/>
                    </com.google.android.material.textfield.TextInputLayout>
                </RelativeLayout>

                <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/manual_logging_image_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/large_margin"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        android:layout_marginLeft="@dimen/base_margin"
                        android:layout_marginRight="@dimen/small_margin"
                        app:spanCount="4"/>

                <com.google.android.material.button.MaterialButton
                        android:id="@+id/manual_logging_button_add_photos"
                        android:layout_width="match_parent"
                        android:layout_height="75dp"
                        android:text="@string/add_your_photos"
                        android:backgroundTint="@color/colorAccent"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:gravity="center"
                        app:iconPadding="@dimen/base_margin"
                        app:icon="@drawable/ic_add_photo"
                        app:cornerRadius="@dimen/radius"
                        app:iconGravity="textStart"
                        android:textAllCaps="false"
                        app:iconTint="@color/white"
                        android:elevation="0dp"
                        android:stateListAnimator="@null"
                        style="@style/Widget.MaterialComponents.Button"/>

                <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/extra_large_margin"
                        app:boxBackgroundMode="none"
                        app:hintEnabled="false"
                        app:errorIconDrawable="@null">
                    <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/review_submit_input_body"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:minHeight="200dp"
                            android:hint="@string/what_happened_out_there_tell_your_story_here"
                            android:inputType="textCapSentences|textMultiLine"
                            android:background="@drawable/bg_input_track"
                            android:paddingTop="12dp"
                            android:paddingBottom="12dp"
                            android:layout_marginBottom="@dimen/base_margin"
                            android:gravity="top"
                            android:textSize="16sp"/>
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
                android:id="@+id/manual_logging_controls"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:clipToPadding="false"
                android:paddingBottom="15dp"
                android:paddingTop="10dp"
                android:layout_alignParentBottom="true"
                android:background="@color/white"
                android:elevation="8dp">

            <Button
                    android:id="@+id/manual_logging_button_cancel"
                    android:text="@string/cancel"
                    android:elevation="2dp"
                    android:layout_marginRight="@dimen/tracking_button_spacing_and_padding"
                    style="@style/TrackerButton.Cancel"/>

            <Button
                    android:id="@+id/manual_logging_button_save"
                    android:text="@string/save"
                    android:elevation="2dp"
                    android:layout_marginLeft="@dimen/tracking_button_spacing_and_padding"
                    style="@style/TrackerButton.Primary"/>
        </LinearLayout>
    </RelativeLayout>
</FrameLayout>