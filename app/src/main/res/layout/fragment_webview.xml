<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/webview_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary">

    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <ImageButton
            android:id="@+id/webview_button_settings"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_settings"
            android:layout_marginRight="@dimen/base_margin"
            android:padding="10dp"
            android:tint="@color/white"
            android:visibility="gone"
            android:background="@null"
            android:layout_alignParentRight="true"/>

    <ProgressBar
            android:id="@+id/webview_loader"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:layout_centerInParent="true"
            android:visibility="visible"/>

    <ImageButton
            android:id="@+id/webview_button_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_back"
            android:layout_marginLeft="9dp"
            android:padding="10dp"
            android:tint="@color/white"
            android:background="@color/transparent"
            android:visibility="gone"/>
</RelativeLayout>
