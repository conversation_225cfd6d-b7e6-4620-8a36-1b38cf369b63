<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        app:cardCornerRadius="@dimen/radius"
        android:layout_marginTop="8dp"
        android:layout_marginRight="@dimen/base_margin"
        android:layout_marginLeft="@dimen/base_margin"
        android:layout_marginBottom="@dimen/small_margin">

    <include layout="@layout/layout_peak_row"/>

</com.google.android.material.card.MaterialCardView>