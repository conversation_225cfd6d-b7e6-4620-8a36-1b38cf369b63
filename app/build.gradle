plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
}

android {
    compileSdk 34
    defaultConfig {
        applicationId "com.peakery.android"
        minSdkVersion 26
        targetSdkVersion 34
        versionCode 243
        versionName "1.2.11"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField "String", "api_url", "\"https://peakery-api.herokuapp.com\""
        buildConfigField "String", "web_url", "\"https://peakerydev.herokuapp.com\""
        setProperty("archivesBaseName", "Peakery-$versionName ($versionCode)")
    }

    signingConfigs {
        release {
            storeFile file('signing')
            storePassword 'peakery'
            keyAlias 'peakery'
            keyPassword 'peakery'
        }
        dev {
            storeFile file('signing')
            storePassword 'peakery'
            keyAlias 'peakery'
            keyPassword 'peakery'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "api_url", "\"https://peakery.com/api/mobile/\""
            buildConfigField "String", "web_url", "\"https://peakery.com\""
            manifestPlaceholders = [usesCleartextTraffic:"false"]
        }
        devRelease {
            minifyEnabled false
            signingConfig signingConfigs.dev
            applicationIdSuffix ".dev"
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "api_url", "\"https://peakerydev.herokuapp.com/api/mobile/\""
            buildConfigField "String", "web_url", "\"https://peakerydev.herokuapp.com\""
            manifestPlaceholders = [usesCleartextTraffic:"false"]
        }
        debug {
            debuggable true
            applicationIdSuffix ".debug"
            versionNameSuffix "-debug"
            buildConfigField "String", "api_url", "\"https://peakerydev.herokuapp.com/api/mobile/\""
            buildConfigField "String", "web_url", "\"https://peakerydev.herokuapp.com\""
            manifestPlaceholders = [usesCleartextTraffic:"true"]
            gradle.taskGraph.whenReady {
                tasks.each { task ->
                    if (task.name.contains("processDebugGoogleServices")) {
                        task.enabled = false
                    }
                }
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }
    buildFeatures {
        viewBinding = true
    }
    sourceSets { main { assets.srcDirs = ['src/main/assets', 'src/main/assets/'] } }
    namespace 'com.peakery.android'
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.annotation:annotation:1.8.2'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'com.google.android.libraries.places:places:3.3.0'
    implementation 'androidx.cardview:cardview:1.0.0'

    // Google Play Services
    implementation "com.google.android.gms:play-services-location:20.0.0"
    implementation 'com.google.android.play:app-update:2.1.0'
    implementation 'com.google.android.play:app-update-ktx:2.1.0'

    // Mapbox
    implementation 'com.mapbox.mapboxsdk:mapbox-android-sdk:9.7.1'
    implementation 'com.mapbox.mapboxsdk:mapbox-android-plugin-annotation-v9:0.9.0'
    implementation 'com.mapbox.mapboxsdk:mapbox-sdk-services:6.15.0'

    // Koin
    implementation "org.koin:koin-core:$koin_version"
    implementation "org.koin:koin-android:$koin_version"
    implementation "org.koin:koin-androidx-scope:$koin_version"
    implementation "org.koin:koin-androidx-viewmodel:$koin_version"

    // rx
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    implementation 'io.reactivex.rxjava2:rxjava:2.2.9'
    implementation 'io.reactivex.rxjava2:rxkotlin:2.3.0'
    implementation 'com.jakewharton.rxbinding2:rxbinding:2.2.0'
    implementation 'com.github.tbruyelle:rxpermissions:0.10.2'
    implementation "com.jakewharton.rxbinding2:rxbinding-kotlin:2.0.0"

    // room
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    implementation "androidx.room:room-rxjava2:$room_version"
    kapt "androidx.room:room-compiler:$room_version"
    // For Kotlin use kapt instead of annotationProcessor

    // firebase
    implementation(platform("com.google.firebase:firebase-bom:33.1.2"))
    implementation("com.google.firebase:firebase-analytics-ktx")
    implementation("com.google.firebase:firebase-crashlytics-ktx")

    // gpx
    implementation 'io.jenetics:jpx:1.7.0'
    implementation 'stax:stax-api:1.0.1'
    implementation 'com.fasterxml:aalto-xml:1.1.1'
    implementation 'com.nbsp:materialfilepicker:1.9.1'

    // retrofit
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:adapter-rxjava2:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_version"

    // preferences
    implementation "androidx.preference:preference-ktx:1.2.1"

    // okhttp
    implementation "com.squareup.okhttp3:okhttp:$okhttp_version"
    implementation "com.squareup.okhttp3:logging-interceptor:$okhttp_version"

    // glide
    implementation 'com.github.bumptech.glide:glide:4.13.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.13.0'

    // image picker
    implementation 'com.zhihu.android:matisse:0.5.3-beta3'

    // seekbar
    api('io.apptik.widget:multislider:1.3.1-SNAPSHOT') { changing = true }

    // battery optimizations
    implementation 'com.waseemsabir:betterypermissionhelper:1.0.3'

    // reactive network
    implementation 'com.github.pwittchen:reactivenetwork-rx2:3.0.6'

    // circular progress
    implementation 'com.mikhaellopez:circularprogressbar:3.0.3'

    // lottie
    implementation "com.airbnb.android:lottie:3.4.1"

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'

    compileOnly 'com.github.pengrad:jdk9-deps:1.0'
}

apply plugin: 'kotlin-parcelize'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
