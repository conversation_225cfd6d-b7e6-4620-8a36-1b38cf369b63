// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.9.23'
    ext.koin_version = '2.0.1'
    ext.room_version = "2.6.1"
    ext.retrofit_version = "2.3.0"
    ext.okhttp_version = "4.12.0"

    repositories {
        google()
        jcenter()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.5.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
    }

    repositories {
        google()
        jcenter()
        maven { url 'https://jitpack.io' }
        maven { url "https://oss.sonatype.org/content/repositories/snapshots" }
        flatDir {
            dirs 'lib'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
